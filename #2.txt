Started by user <PERSON><PERSON> Tran
Obtained Jenkins<PERSON><PERSON> from git https://github.com/rainscales-qc-automation/clone-clo-auto.git
[Pipeline] Start of Pipeline
[Pipeline] node
Running on Jenkins in /var/lib/jenkins/workspace/v-job
[Pipeline] {
[Pipeline] stage
[Pipeline] { (Declarative: Checkout SCM)
[Pipeline] checkout
The recommended git tool is: git
using credential 348a7930-21a5-4eb7-bad5-9b230cce1689
Cloning the remote Git repository
Cloning repository https://github.com/rainscales-qc-automation/clone-clo-auto.git
 > git init /var/lib/jenkins/workspace/v-job # timeout=10
Fetching upstream changes from https://github.com/rainscales-qc-automation/clone-clo-auto.git
 > git --version # timeout=10
 > git --version # 'git version 2.25.1'
using GIT_ASKPASS to set credentials 
 > git fetch --tags --force --progress -- https://github.com/rainscales-qc-automation/clone-clo-auto.git +refs/heads/*:refs/remotes/origin/* # timeout=10
 > git config remote.origin.url https://github.com/rainscales-qc-automation/clone-clo-auto.git # timeout=10
 > git config --add remote.origin.fetch +refs/heads/*:refs/remotes/origin/* # timeout=10
Avoid second fetch
 > git rev-parse refs/remotes/origin/main^{commit} # timeout=10
Checking out Revision e317283a751fade354df0ed02cb142059ca4effd (refs/remotes/origin/main)
 > git config core.sparsecheckout # timeout=10
 > git checkout -f e317283a751fade354df0ed02cb142059ca4effd # timeout=10
Commit message: "Merge pull request #1 from rainscales-qc-automation/viet_main"
First time build. Skipping changelog.
[Pipeline] }
[Pipeline] // stage
[Pipeline] withEnv
[Pipeline] {
[Pipeline] withEnv
[Pipeline] {
[Pipeline] stage
[Pipeline] { (Checkout)
[Pipeline] echo
Checking out code from repository...
[Pipeline] checkout
The recommended git tool is: git
using credential 348a7930-21a5-4eb7-bad5-9b230cce1689
 > git rev-parse --resolve-git-dir /var/lib/jenkins/workspace/v-job/.git # timeout=10
Fetching changes from the remote Git repository
 > git config remote.origin.url https://github.com/rainscales-qc-automation/clone-clo-auto.git # timeout=10
Fetching upstream changes from https://github.com/rainscales-qc-automation/clone-clo-auto.git
 > git --version # timeout=10
 > git --version # 'git version 2.25.1'
using GIT_ASKPASS to set credentials 
 > git fetch --tags --force --progress -- https://github.com/rainscales-qc-automation/clone-clo-auto.git +refs/heads/*:refs/remotes/origin/* # timeout=10
 > git rev-parse refs/remotes/origin/main^{commit} # timeout=10
Checking out Revision e317283a751fade354df0ed02cb142059ca4effd (refs/remotes/origin/main)
 > git config core.sparsecheckout # timeout=10
 > git checkout -f e317283a751fade354df0ed02cb142059ca4effd # timeout=10
Commit message: "Merge pull request #1 from rainscales-qc-automation/viet_main"
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Setup Python Environment)
[Pipeline] echo
Setting up Python 3.10 virtual environment...
[Pipeline] sh
+ [ -d robot_test_env ]
+ python3.10 -m venv robot_test_env
/var/lib/jenkins/workspace/v-job@tmp/durable-ed293b4f/script.sh.copy: 8: python3.10: not found
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Run Robot Framework Tests)
Stage "Run Robot Framework Tests" skipped due to earlier failure(s)
[Pipeline] getContext
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Archive Results)
Stage "Archive Results" skipped due to earlier failure(s)
[Pipeline] getContext
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Declarative: Post Actions)
[Pipeline] echo
Cleaning up...
[Pipeline] sh
+ [ -d robot_test_env ]
[Pipeline] echo
Pipeline failed!
[Pipeline] }
[Pipeline] // stage
[Pipeline] }
[Pipeline] // withEnv
[Pipeline] }
[Pipeline] // withEnv
[Pipeline] }
[Pipeline] // node
[Pipeline] End of Pipeline
ERROR: script returned exit code 127
Finished: FAILURE
