Started by an SCM change
Obtained Jenkins<PERSON><PERSON> from git https://github.com/rainscales-qc-automation/clone-clo-auto.git
[Pipeline] Start of Pipeline
[Pipeline] node
Running on Jenkins
 in /var/lib/jenkins/workspace/v-job
[Pipeline] {
[Pipeline] stage
[Pipeline] { (Declarative: Checkout SCM)
[Pipeline] checkout
The recommended git tool is: git
using credential 348a7930-21a5-4eb7-bad5-9b230cce1689
 > git rev-parse --resolve-git-dir /var/lib/jenkins/workspace/v-job/.git # timeout=10
Fetching changes from the remote Git repository
 > git config remote.origin.url https://github.com/rainscales-qc-automation/clone-clo-auto.git # timeout=10
Fetching upstream changes from https://github.com/rainscales-qc-automation/clone-clo-auto.git
 > git --version # timeout=10
 > git --version # 'git version 2.25.1'
using GIT_ASKPASS to set credentials 
 > git fetch --tags --force --progress -- https://github.com/rainscales-qc-automation/clone-clo-auto.git +refs/heads/*:refs/remotes/origin/* # timeout=10
 > git rev-parse refs/remotes/origin/main^{commit} # timeout=10
Checking out Revision f9a4a880f02451715bba7feec2df3e9bdfd193d7 (refs/remotes/origin/main)
 > git config core.sparsecheckout # timeout=10
 > git checkout -f f9a4a880f02451715bba7feec2df3e9bdfd193d7 # timeout=10
Commit message: "rrr e"
 > git rev-list --no-walk 151864bfac858a52b64cecda904127ab18038171 # timeout=10
[Pipeline] }
[Pipeline] // stage
[Pipeline] withEnv
[Pipeline] {
[Pipeline] withEnv
[Pipeline] {
[Pipeline] timeout
Timeout set to expire in 30 min
[Pipeline] {
[Pipeline] timestamps
[Pipeline] {
[Pipeline] stage
[Pipeline] { (Setup & Run Tests)
[Pipeline] echo
15:29:12  Setting up environment and running tests...
[Pipeline] sh
15:29:13  + python3 --version
15:29:13  Python 3.8.10
15:29:13  + pip3 install --user --upgrade pip
15:29:17  Collecting pip
15:29:17    Using cached pip-25.0.1-py3-none-any.whl (1.8 MB)
15:29:19  Installing collected packages: pip
15:29:20    WARNING: The scripts pip, pip3 and pip3.8 are installed in '/var/lib/jenkins/.local/bin' which is not on PATH.
15:29:20    Consider adding this directory to PATH or, if you prefer to suppress this warning, use --no-warn-script-location.
15:29:21  Successfully installed pip-25.0.1
15:29:21  + pip3 install --user -r requirements.txt
15:29:23  Requirement already satisfied: robotframework==7.3.1 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from -r requirements.txt (line 1)) (7.3.1)
15:29:23  Requirement already satisfied: robotframework-seleniumlibrary==6.7.1 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from -r requirements.txt (line 2)) (6.7.1)
15:29:23  Requirement already satisfied: gspread==6.2.1 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from -r requirements.txt (line 3)) (6.2.1)
15:29:23  Requirement already satisfied: selenium>=4.3.0 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (4.27.1)
15:29:23  Requirement already satisfied: click>=8.0 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (8.1.8)
15:29:23  Requirement already satisfied: robotframework-pythonlibcore>=4.4.1 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (4.4.1)
15:29:23  Requirement already satisfied: google-auth-oauthlib>=0.4.1 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from gspread==6.2.1->-r requirements.txt (line 3)) (1.2.2)
15:29:23  Requirement already satisfied: google-auth>=1.12.0 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from gspread==6.2.1->-r requirements.txt (line 3)) (2.40.3)
15:29:23  Requirement already satisfied: trio-websocket~=0.9 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (0.12.2)
15:29:23  Requirement already satisfied: urllib3[socks]<3,>=1.26 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (2.2.3)
15:29:23  Requirement already satisfied: typing_extensions~=4.9 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (4.13.2)
15:29:23  Requirement already satisfied: websocket-client~=1.8 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (1.8.0)
15:29:23  Requirement already satisfied: certifi>=2021.10.8 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (2025.7.14)
15:29:23  Requirement already satisfied: trio~=0.17 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (0.27.0)
15:29:23  Requirement already satisfied: requests-oauthlib>=0.7.0 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from google-auth-oauthlib>=0.4.1->gspread==6.2.1->-r requirements.txt (line 3)) (2.0.0)
15:29:23  Requirement already satisfied: cachetools<6.0,>=2.0.0 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from google-auth>=1.12.0->gspread==6.2.1->-r requirements.txt (line 3)) (5.5.2)
15:29:23  Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/lib/python3/dist-packages (from google-auth>=1.12.0->gspread==6.2.1->-r requirements.txt (line 3)) (0.2.1)
15:29:23  Requirement already satisfied: rsa<5,>=3.1.4 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from google-auth>=1.12.0->gspread==6.2.1->-r requirements.txt (line 3)) (4.9.1)
15:29:23  Requirement already satisfied: outcome>=1.2.0 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from trio-websocket~=0.9->selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (1.3.0.post0)
15:29:23  Requirement already satisfied: wsproto>=0.14 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from trio-websocket~=0.9->selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (1.2.0)
15:29:23  Requirement already satisfied: exceptiongroup; python_version < "3.11" in /usr/local/lib/python3.8/dist-packages (from trio-websocket~=0.9->selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (1.1.3)
15:29:23  Requirement already satisfied: pysocks!=1.5.7,<2.0,>=1.5.6; extra == "socks" in /var/lib/jenkins/.local/lib/python3.8/site-packages (from urllib3[socks]<3,>=1.26->selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (1.7.1)
15:29:23  Requirement already satisfied: attrs>=23.2.0 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from trio~=0.17->selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (25.3.0)
15:29:23  Requirement already satisfied: sniffio>=1.3.0 in /usr/local/lib/python3.8/dist-packages (from trio~=0.17->selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (1.3.0)
15:29:23  Requirement already satisfied: idna in /usr/lib/python3/dist-packages (from trio~=0.17->selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (2.8)
15:29:23  Requirement already satisfied: sortedcontainers in /var/lib/jenkins/.local/lib/python3.8/site-packages (from trio~=0.17->selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (2.4.0)
15:29:23  Requirement already satisfied: oauthlib>=3.0.0 in /usr/lib/python3/dist-packages (from requests-oauthlib>=0.7.0->google-auth-oauthlib>=0.4.1->gspread==6.2.1->-r requirements.txt (line 3)) (3.1.0)
15:29:23  Requirement already satisfied: requests>=2.0.0 in /usr/local/lib/python3.8/dist-packages (from requests-oauthlib>=0.7.0->google-auth-oauthlib>=0.4.1->gspread==6.2.1->-r requirements.txt (line 3)) (2.31.0)
15:29:23  Requirement already satisfied: pyasn1>=0.1.3 in /usr/lib/python3/dist-packages (from rsa<5,>=3.1.4->google-auth>=1.12.0->gspread==6.2.1->-r requirements.txt (line 3)) (0.4.2)
15:29:23  Requirement already satisfied: h11<1,>=0.9.0 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from wsproto>=0.14->trio-websocket~=0.9->selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (0.16.0)
15:29:23  Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.8/dist-packages (from requests>=2.0.0->requests-oauthlib>=0.7.0->google-auth-oauthlib>=0.4.1->gspread==6.2.1->-r requirements.txt (line 3)) (3.3.1)
15:29:24  + export PATH=/var/lib/jenkins/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/snap/bin
15:29:24  + python3 test_robot_command.py
15:29:26  Testing Robot Framework command availability...
15:29:26  ==================================================
15:29:26  ✓ Found robot command at: /var/lib/jenkins/.local/bin/robot
15:29:26  ✓ Robot version: Robot Framework 7.3.1 (Python 3.8.10 on linux)
15:29:26  
15:29:26  ==================================================
15:29:26  Testing installed packages...
15:29:26  ✓ robotframework imported successfully
15:29:26  ✓ Robot Framework version: 7.3.1
15:29:26  ✓ SeleniumLibrary imported successfully
15:29:26  ✓ gspread imported successfully
15:29:26  
15:29:26  ==================================================
15:29:26  SUMMARY
15:29:26  ==================================================
15:29:26  ✓ Robot command: /var/lib/jenkins/.local/bin/robot 
15:29:26  ✓ Robot Framework is ready to use!
15:29:26  + mkdir -p results
15:29:26  + python3 run_tests.py
15:29:31  Robot Framework Test Runner
15:29:31  ========================================
15:29:31  ============================================================
15:29:31  STARTING ROBOT FRAMEWORK TEST EXECUTION CYCLE
15:29:31  ============================================================
15:29:31  Starting Robot Framework test execution at 2025-07-31 15:29:26.549677
15:29:31  Test directory: /var/lib/jenkins/workspace/v-job/test
15:29:31  Results directory: /var/lib/jenkins/workspace/v-job/results
15:29:31  Executing command: /var/lib/jenkins/.local/bin/robot --outputdir /var/lib/jenkins/workspace/v-job/results --output /var/lib/jenkins/workspace/v-job/results/output.xml --log /var/lib/jenkins/workspace/v-job/results/log.html --report /var/lib/jenkins/workspace/v-job/results/report.html --loglevel INFO /var/lib/jenkins/workspace/v-job/test
15:29:31  Robot Framework execution completed with return code: 34
15:29:31  STDOUT:
15:29:31  ==============================================================================
15:29:31  Test                                                                          
15:29:31  ==============================================================================
15:29:31  Test.Add Assessment Test                                                      
15:29:31  ==============================================================================
15:29:31  Verify Form Add Assessment is Displayed :: Test case to verify the... | FAIL |
15:29:31  Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x5587154412ca <unknown>
15:29:31  #1 0x558714ee8550 <unknown>
15:29:31  #2 0x558714f229cb <unknown>
15:29:31  #3 0x558714f1dd17 <unknown>
15:29:31  #4 0x558714f6e19e <unknown>
15:29:31  #5 0x558714f6d766 <unknown>
15:29:31  #6 0x558714f5f993 <unknown>
15:29:31  #7 0x558714f2bd6b <unknown>
15:29:31  #8 0x558714f2d141 <unknown>
15:29:31  #9 0x5587154062ab <unknown>
15:29:31  #10 0x55871540a0b9 <unknown>
15:29:31  #11 0x5587153ed139 <unknown>
15:29:31  #12 0x55871540ac68 <unknown>
15:29:31  #13 0x5587153d160f <unknown>
15:29:31  #14 0x55871542f1f8 <unknown>
15:29:31  #15 0x55871542f3d6 <unknown>
15:29:31  #16 0x5587154405e6 <unknown>
15:29:31  #17 0x7fabd9963609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Verify Add Assessment Successfully :: Test case to add an assessment  | FAIL |
15:29:31  Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x5626e79dc2ca <unknown>
15:29:31  #1 0x5626e7483550 <unknown>
15:29:31  #2 0x5626e74bd9cb <unknown>
15:29:31  #3 0x5626e74b8d17 <unknown>
15:29:31  #4 0x5626e750919e <unknown>
15:29:31  #5 0x5626e7508766 <unknown>
15:29:31  #6 0x5626e74fa993 <unknown>
15:29:31  #7 0x5626e74c6d6b <unknown>
15:29:31  #8 0x5626e74c8141 <unknown>
15:29:31  #9 0x5626e79a12ab <unknown>
15:29:31  #10 0x5626e79a50b9 <unknown>
15:29:31  #11 0x5626e7988139 <unknown>
15:29:31  #12 0x5626e79a5c68 <unknown>
15:29:31  #13 0x5626e796c60f <unknown>
15:29:31  #14 0x5626e79ca1f8 <unknown>
15:29:31  #15 0x5626e79ca3d6 <unknown>
15:29:31  #16 0x5626e79db5e6 <unknown>
15:29:31  #17 0x7f3d97472609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Verify Leave Required Fields Empty :: Test case Bỏ trống các trườn... | FAIL |
15:29:31  Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x556cb82722ca <unknown>
15:29:31  #1 0x556cb7d19550 <unknown>
15:29:31  #2 0x556cb7d539cb <unknown>
15:29:31  #3 0x556cb7d4ed17 <unknown>
15:29:31  #4 0x556cb7d9f19e <unknown>
15:29:31  #5 0x556cb7d9e766 <unknown>
15:29:31  #6 0x556cb7d90993 <unknown>
15:29:31  #7 0x556cb7d5cd6b <unknown>
15:29:31  #8 0x556cb7d5e141 <unknown>
15:29:31  #9 0x556cb82372ab <unknown>
15:29:31  #10 0x556cb823b0b9 <unknown>
15:29:31  #11 0x556cb821e139 <unknown>
15:29:31  #12 0x556cb823bc68 <unknown>
15:29:31  #13 0x556cb820260f <unknown>
15:29:31  #14 0x556cb82601f8 <unknown>
15:29:31  #15 0x556cb82603d6 <unknown>
15:29:31  #16 0x556cb82715e6 <unknown>
15:29:31  #17 0x7f435c70d609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Verify Fill Assessment With HighWeight :: Nhập tỷ trọng > 100         | FAIL |
15:29:31  Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x5588325b42ca <unknown>
15:29:31  #1 0x55883205b550 <unknown>
15:29:31  #2 0x5588320959cb <unknown>
15:29:31  #3 0x558832090d17 <unknown>
15:29:31  #4 0x5588320e119e <unknown>
15:29:31  #5 0x5588320e0766 <unknown>
15:29:31  #6 0x5588320d2993 <unknown>
15:29:31  #7 0x55883209ed6b <unknown>
15:29:31  #8 0x5588320a0141 <unknown>
15:29:31  #9 0x5588325792ab <unknown>
15:29:31  #10 0x55883257d0b9 <unknown>
15:29:31  #11 0x558832560139 <unknown>
15:29:31  #12 0x55883257dc68 <unknown>
15:29:31  #13 0x55883254460f <unknown>
15:29:31  #14 0x5588325a21f8 <unknown>
15:29:31  #15 0x5588325a23d6 <unknown>
15:29:31  #16 0x5588325b35e6 <unknown>
15:29:31  #17 0x7fce95e78609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Verify Fill Assessment WithInvalidPassScore :: Điểm đạt > Tổng điểm   | FAIL |
15:29:31  Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x558b34cd32ca <unknown>
15:29:31  #1 0x558b3477a550 <unknown>
15:29:31  #2 0x558b347b49cb <unknown>
15:29:31  #3 0x558b347afd17 <unknown>
15:29:31  #4 0x558b3480019e <unknown>
15:29:31  #5 0x558b347ff766 <unknown>
15:29:31  #6 0x558b347f1993 <unknown>
15:29:31  #7 0x558b347bdd6b <unknown>
15:29:31  #8 0x558b347bf141 <unknown>
15:29:31  #9 0x558b34c982ab <unknown>
15:29:31  #10 0x558b34c9c0b9 <unknown>
15:29:31  #11 0x558b34c7f139 <unknown>
15:29:31  #12 0x558b34c9cc68 <unknown>
15:29:31  #13 0x558b34c6360f <unknown>
15:29:31  #14 0x558b34cc11f8 <unknown>
15:29:31  #15 0x558b34cc13d6 <unknown>
15:29:31  #16 0x558b34cd25e6 <unknown>
15:29:31  #17 0x7f742a659609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Verify Fill Assessment WithSpecialDescription :: Tên đánh giá có k... | FAIL |
15:29:31  Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x56214acd62ca <unknown>
15:29:31  #1 0x56214a77d550 <unknown>
15:29:31  #2 0x56214a7b79cb <unknown>
15:29:31  #3 0x56214a7b2d17 <unknown>
15:29:31  #4 0x56214a80319e <unknown>
15:29:31  #5 0x56214a802766 <unknown>
15:29:31  #6 0x56214a7f4993 <unknown>
15:29:31  #7 0x56214a7c0d6b <unknown>
15:29:31  #8 0x56214a7c2141 <unknown>
15:29:31  #9 0x56214ac9b2ab <unknown>
15:29:31  #10 0x56214ac9f0b9 <unknown>
15:29:31  #11 0x56214ac82139 <unknown>
15:29:31  #12 0x56214ac9fc68 <unknown>
15:29:31  #13 0x56214ac6660f <unknown>
15:29:31  #14 0x56214acc41f8 <unknown>
15:29:31  #15 0x56214acc43d6 <unknown>
15:29:31  #16 0x56214acd55e6 <unknown>
15:29:31  #17 0x7f4718645609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Test.Add Assessment Test                                              | FAIL |
15:29:31  6 tests, 0 passed, 6 failed
15:29:31  ==============================================================================
15:29:31  Test.Browser Questions Test                                                   
15:29:31  ==============================================================================
15:29:31  TC_BrowserQs_001: Approve Question :: User Admin Approve Question     | FAIL |
15:29:31  Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55589fec82ca <unknown>
15:29:31  #1 0x55589f96f550 <unknown>
15:29:31  #2 0x55589f9a99cb <unknown>
15:29:31  #3 0x55589f9a4d17 <unknown>
15:29:31  #4 0x55589f9f519e <unknown>
15:29:31  #5 0x55589f9f4766 <unknown>
15:29:31  #6 0x55589f9e6993 <unknown>
15:29:31  #7 0x55589f9b2d6b <unknown>
15:29:31  #8 0x55589f9b4141 <unknown>
15:29:31  #9 0x55589fe8d2ab <unknown>
15:29:31  #10 0x55589fe910b9 <unknown>
15:29:31  #11 0x55589fe74139 <unknown>
15:29:31  #12 0x55589fe91c68 <unknown>
15:29:31  #13 0x55589fe5860f <unknown>
15:29:31  #14 0x55589feb61f8 <unknown>
15:29:31  #15 0x55589feb63d6 <unknown>
15:29:31  #16 0x55589fec75e6 <unknown>
15:29:31  #17 0x7ffa4bc7e609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  TC_BrowserQs_002: Reject Question :: User Admin Reject Question       | FAIL |
15:29:31  Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55d2776da2ca <unknown>
15:29:31  #1 0x55d277181550 <unknown>
15:29:31  #2 0x55d2771bb9cb <unknown>
15:29:31  #3 0x55d2771b6d17 <unknown>
15:29:31  #4 0x55d27720719e <unknown>
15:29:31  #5 0x55d277206766 <unknown>
15:29:31  #6 0x55d2771f8993 <unknown>
15:29:31  #7 0x55d2771c4d6b <unknown>
15:29:31  #8 0x55d2771c6141 <unknown>
15:29:31  #9 0x55d27769f2ab <unknown>
15:29:31  #10 0x55d2776a30b9 <unknown>
15:29:31  #11 0x55d277686139 <unknown>
15:29:31  #12 0x55d2776a3c68 <unknown>
15:29:31  #13 0x55d27766a60f <unknown>
15:29:31  #14 0x55d2776c81f8 <unknown>
15:29:31  #15 0x55d2776c83d6 <unknown>
15:29:31  #16 0x55d2776d95e6 <unknown>
15:29:31  #17 0x7f09bd703609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  TC_BrowserQs_003: Filter Course :: Filter Course: CS301: Cơ sở dữ ... | FAIL |
15:29:31  Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55a68efe92ca <unknown>
15:29:31  #1 0x55a68ea90550 <unknown>
15:29:31  #2 0x55a68eaca9cb <unknown>
15:29:31  #3 0x55a68eac5d17 <unknown>
15:29:31  #4 0x55a68eb1619e <unknown>
15:29:31  #5 0x55a68eb15766 <unknown>
15:29:31  #6 0x55a68eb07993 <unknown>
15:29:31  #7 0x55a68ead3d6b <unknown>
15:29:31  #8 0x55a68ead5141 <unknown>
15:29:31  #9 0x55a68efae2ab <unknown>
15:29:31  #10 0x55a68efb20b9 <unknown>
15:29:31  #11 0x55a68ef95139 <unknown>
15:29:31  #12 0x55a68efb2c68 <unknown>
15:29:31  #13 0x55a68ef7960f <unknown>
15:29:31  #14 0x55a68efd71f8 <unknown>
15:29:31  #15 0x55a68efd73d6 <unknown>
15:29:31  #16 0x55a68efe85e6 <unknown>
15:29:31  #17 0x7f2a2bf79609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Test.Browser Questions Test                                           | FAIL |
15:29:31  3 tests, 0 passed, 3 failed
15:29:31  ==============================================================================
15:29:31  Test.Chapter Test                                                             
15:29:31  ==============================================================================
15:29:31  Tcs 01: Func-Chapter: Create Chapter with fully field :: Kiểm tra ... | FAIL |
15:29:31  Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55d461e762ca <unknown>
15:29:31  #1 0x55d46191d550 <unknown>
15:29:31  #2 0x55d4619579cb <unknown>
15:29:31  #3 0x55d461952d17 <unknown>
15:29:31  #4 0x55d4619a319e <unknown>
15:29:31  #5 0x55d4619a2766 <unknown>
15:29:31  #6 0x55d461994993 <unknown>
15:29:31  #7 0x55d461960d6b <unknown>
15:29:31  #8 0x55d461962141 <unknown>
15:29:31  #9 0x55d461e3b2ab <unknown>
15:29:31  #10 0x55d461e3f0b9 <unknown>
15:29:31  #11 0x55d461e22139 <unknown>
15:29:31  #12 0x55d461e3fc68 <unknown>
15:29:31  #13 0x55d461e0660f <unknown>
15:29:31  #14 0x55d461e641f8 <unknown>
15:29:31  #15 0x55d461e643d6 <unknown>
15:29:31  #16 0x55d461e755e6 <unknown>
15:29:31  #17 0x7faaa9cdd609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Test.Chapter Test                                                     | FAIL |
15:29:31  1 test, 0 passed, 1 failed
15:29:31  ==============================================================================
15:29:31  Test.Course Test                                                              
15:29:31  ==============================================================================
15:29:31  Tcs 01: Func-Course-03: Create Course with fully field :: Kiểm tra... | FAIL |
15:29:31  Parent suite setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55863634f2ca <unknown>
15:29:31  #1 0x558635df6550 <unknown>
15:29:31  #2 0x558635e309cb <unknown>
15:29:31  #3 0x558635e2bd17 <unknown>
15:29:31  #4 0x558635e7c19e <unknown>
15:29:31  #5 0x558635e7b766 <unknown>
15:29:31  #6 0x558635e6d993 <unknown>
15:29:31  #7 0x558635e39d6b <unknown>
15:29:31  #8 0x558635e3b141 <unknown>
15:29:31  #9 0x5586363142ab <unknown>
15:29:31  #10 0x5586363180b9 <unknown>
15:29:31  #11 0x5586362fb139 <unknown>
15:29:31  #12 0x558636318c68 <unknown>
15:29:31  #13 0x5586362df60f <unknown>
15:29:31  #14 0x55863633d1f8 <unknown>
15:29:31  #15 0x55863633d3d6 <unknown>
15:29:31  #16 0x55863634e5e6 <unknown>
15:29:31  #17 0x7fd685430609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Tcs 02: Func-Course-04: Create course with empty required fields :... | FAIL |
15:29:31  Parent suite setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55863634f2ca <unknown>
15:29:31  #1 0x558635df6550 <unknown>
15:29:31  #2 0x558635e309cb <unknown>
15:29:31  #3 0x558635e2bd17 <unknown>
15:29:31  #4 0x558635e7c19e <unknown>
15:29:31  #5 0x558635e7b766 <unknown>
15:29:31  #6 0x558635e6d993 <unknown>
15:29:31  #7 0x558635e39d6b <unknown>
15:29:31  #8 0x558635e3b141 <unknown>
15:29:31  #9 0x5586363142ab <unknown>
15:29:31  #10 0x5586363180b9 <unknown>
15:29:31  #11 0x5586362fb139 <unknown>
15:29:31  #12 0x558636318c68 <unknown>
15:29:31  #13 0x5586362df60f <unknown>
15:29:31  #14 0x55863633d1f8 <unknown>
15:29:31  #15 0x55863633d3d6 <unknown>
15:29:31  #16 0x55863634e5e6 <unknown>
15:29:31  #17 0x7fd685430609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Tcs 11: Func-Course: Create Course with maximum credits value :: K... | FAIL |
15:29:31  Parent suite setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55863634f2ca <unknown>
15:29:31  #1 0x558635df6550 <unknown>
15:29:31  #2 0x558635e309cb <unknown>
15:29:31  #3 0x558635e2bd17 <unknown>
15:29:31  #4 0x558635e7c19e <unknown>
15:29:31  #5 0x558635e7b766 <unknown>
15:29:31  #6 0x558635e6d993 <unknown>
15:29:31  #7 0x558635e39d6b <unknown>
15:29:31  #8 0x558635e3b141 <unknown>
15:29:31  #9 0x5586363142ab <unknown>
15:29:31  #10 0x5586363180b9 <unknown>
15:29:31  #11 0x5586362fb139 <unknown>
15:29:31  #12 0x558636318c68 <unknown>
15:29:31  #13 0x5586362df60f <unknown>
15:29:31  #14 0x55863633d1f8 <unknown>
15:29:31  #15 0x55863633d3d6 <unknown>
15:29:31  #16 0x55863634e5e6 <unknown>
15:29:31  #17 0x7fd685430609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Tcs 03: Func-CLO-03: Create CLOs with fully fields :: Kiểm tra việ... | FAIL |
15:29:31  Parent suite setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55863634f2ca <unknown>
15:29:31  #1 0x558635df6550 <unknown>
15:29:31  #2 0x558635e309cb <unknown>
15:29:31  #3 0x558635e2bd17 <unknown>
15:29:31  #4 0x558635e7c19e <unknown>
15:29:31  #5 0x558635e7b766 <unknown>
15:29:31  #6 0x558635e6d993 <unknown>
15:29:31  #7 0x558635e39d6b <unknown>
15:29:31  #8 0x558635e3b141 <unknown>
15:29:31  #9 0x5586363142ab <unknown>
15:29:31  #10 0x5586363180b9 <unknown>
15:29:31  #11 0x5586362fb139 <unknown>
15:29:31  #12 0x558636318c68 <unknown>
15:29:31  #13 0x5586362df60f <unknown>
15:29:31  #14 0x55863633d1f8 <unknown>
15:29:31  #15 0x55863633d3d6 <unknown>
15:29:31  #16 0x55863634e5e6 <unknown>
15:29:31  #17 0x7fd685430609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Tcs 04: Func-CLO-04: Create CLOS with empty required fields :: Tạo... | FAIL |
15:29:31  Parent suite setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55863634f2ca <unknown>
15:29:31  #1 0x558635df6550 <unknown>
15:29:31  #2 0x558635e309cb <unknown>
15:29:31  #3 0x558635e2bd17 <unknown>
15:29:31  #4 0x558635e7c19e <unknown>
15:29:31  #5 0x558635e7b766 <unknown>
15:29:31  #6 0x558635e6d993 <unknown>
15:29:31  #7 0x558635e39d6b <unknown>
15:29:31  #8 0x558635e3b141 <unknown>
15:29:31  #9 0x5586363142ab <unknown>
15:29:31  #10 0x5586363180b9 <unknown>
15:29:31  #11 0x5586362fb139 <unknown>
15:29:31  #12 0x558636318c68 <unknown>
15:29:31  #13 0x5586362df60f <unknown>
15:29:31  #14 0x55863633d1f8 <unknown>
15:29:31  #15 0x55863633d3d6 <unknown>
15:29:31  #16 0x55863634e5e6 <unknown>
15:29:31  #17 0x7fd685430609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Tcs 06: Edit Course :: Chỉnh sửa thông tin khoá học đã tạo            | FAIL |
15:29:31  Parent suite setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55863634f2ca <unknown>
15:29:31  #1 0x558635df6550 <unknown>
15:29:31  #2 0x558635e309cb <unknown>
15:29:31  #3 0x558635e2bd17 <unknown>
15:29:31  #4 0x558635e7c19e <unknown>
15:29:31  #5 0x558635e7b766 <unknown>
15:29:31  #6 0x558635e6d993 <unknown>
15:29:31  #7 0x558635e39d6b <unknown>
15:29:31  #8 0x558635e3b141 <unknown>
15:29:31  #9 0x5586363142ab <unknown>
15:29:31  #10 0x5586363180b9 <unknown>
15:29:31  #11 0x5586362fb139 <unknown>
15:29:31  #12 0x558636318c68 <unknown>
15:29:31  #13 0x5586362df60f <unknown>
15:29:31  #14 0x55863633d1f8 <unknown>
15:29:31  #15 0x55863633d3d6 <unknown>
15:29:31  #16 0x55863634e5e6 <unknown>
15:29:31  #17 0x7fd685430609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Tcs 08: Compare value CLO in course :: So sánh giá trị CLO đã định... | FAIL |
15:29:31  Parent suite setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55863634f2ca <unknown>
15:29:31  #1 0x558635df6550 <unknown>
15:29:31  #2 0x558635e309cb <unknown>
15:29:31  #3 0x558635e2bd17 <unknown>
15:29:31  #4 0x558635e7c19e <unknown>
15:29:31  #5 0x558635e7b766 <unknown>
15:29:31  #6 0x558635e6d993 <unknown>
15:29:31  #7 0x558635e39d6b <unknown>
15:29:31  #8 0x558635e3b141 <unknown>
15:29:31  #9 0x5586363142ab <unknown>
15:29:31  #10 0x5586363180b9 <unknown>
15:29:31  #11 0x5586362fb139 <unknown>
15:29:31  #12 0x558636318c68 <unknown>
15:29:31  #13 0x5586362df60f <unknown>
15:29:31  #14 0x55863633d1f8 <unknown>
15:29:31  #15 0x55863633d3d6 <unknown>
15:29:31  #16 0x55863634e5e6 <unknown>
15:29:31  #17 0x7fd685430609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  TC 09: Edit All Fields In Course :: Kiểm tra chỉnh sửa giá trị vào... | FAIL |
15:29:31  Parent suite setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55863634f2ca <unknown>
15:29:31  #1 0x558635df6550 <unknown>
15:29:31  #2 0x558635e309cb <unknown>
15:29:31  #3 0x558635e2bd17 <unknown>
15:29:31  #4 0x558635e7c19e <unknown>
15:29:31  #5 0x558635e7b766 <unknown>
15:29:31  #6 0x558635e6d993 <unknown>
15:29:31  #7 0x558635e39d6b <unknown>
15:29:31  #8 0x558635e3b141 <unknown>
15:29:31  #9 0x5586363142ab <unknown>
15:29:31  #10 0x5586363180b9 <unknown>
15:29:31  #11 0x5586362fb139 <unknown>
15:29:31  #12 0x558636318c68 <unknown>
15:29:31  #13 0x5586362df60f <unknown>
15:29:31  #14 0x55863633d1f8 <unknown>
15:29:31  #15 0x55863633d3d6 <unknown>
15:29:31  #16 0x55863634e5e6 <unknown>
15:29:31  #17 0x7fd685430609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Tcs 12: "Tạo mới" button inactive :: Kiểm tra nút "Tạo mới" không ... | FAIL |
15:29:31  Parent suite setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55863634f2ca <unknown>
15:29:31  #1 0x558635df6550 <unknown>
15:29:31  #2 0x558635e309cb <unknown>
15:29:31  #3 0x558635e2bd17 <unknown>
15:29:31  #4 0x558635e7c19e <unknown>
15:29:31  #5 0x558635e7b766 <unknown>
15:29:31  #6 0x558635e6d993 <unknown>
15:29:31  #7 0x558635e39d6b <unknown>
15:29:31  #8 0x558635e3b141 <unknown>
15:29:31  #9 0x5586363142ab <unknown>
15:29:31  #10 0x5586363180b9 <unknown>
15:29:31  #11 0x5586362fb139 <unknown>
15:29:31  #12 0x558636318c68 <unknown>
15:29:31  #13 0x5586362df60f <unknown>
15:29:31  #14 0x55863633d1f8 <unknown>
15:29:31  #15 0x55863633d3d6 <unknown>
15:29:31  #16 0x55863634e5e6 <unknown>
15:29:31  #17 0x7fd685430609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Tc 13: Find word with valid and invalid Keyword :: Tìm kiếm từ kho... | FAIL |
15:29:31  Parent suite setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55863634f2ca <unknown>
15:29:31  #1 0x558635df6550 <unknown>
15:29:31  #2 0x558635e309cb <unknown>
15:29:31  #3 0x558635e2bd17 <unknown>
15:29:31  #4 0x558635e7c19e <unknown>
15:29:31  #5 0x558635e7b766 <unknown>
15:29:31  #6 0x558635e6d993 <unknown>
15:29:31  #7 0x558635e39d6b <unknown>
15:29:31  #8 0x558635e3b141 <unknown>
15:29:31  #9 0x5586363142ab <unknown>
15:29:31  #10 0x5586363180b9 <unknown>
15:29:31  #11 0x5586362fb139 <unknown>
15:29:31  #12 0x558636318c68 <unknown>
15:29:31  #13 0x5586362df60f <unknown>
15:29:31  #14 0x55863633d1f8 <unknown>
15:29:31  #15 0x55863633d3d6 <unknown>
15:29:31  #16 0x55863634e5e6 <unknown>
15:29:31  #17 0x7fd685430609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Test.Course Test                                                      | FAIL |
15:29:31  Suite setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55863634f2ca <unknown>
15:29:31  #1 0x558635df6550 <unknown>
15:29:31  #2 0x558635e309cb <unknown>
15:29:31  #3 0x558635e2bd17 <unknown>
15:29:31  #4 0x558635e7c19e <unknown>
15:29:31  #5 0x558635e7b766 <unknown>
15:29:31  #6 0x558635e6d993 <unknown>
15:29:31  #7 0x558635e39d6b <unknown>
15:29:31  #8 0x558635e3b141 <unknown>
15:29:31  #9 0x5586363142ab <unknown>
15:29:31  #10 0x5586363180b9 <unknown>
15:29:31  #11 0x5586362fb139 <unknown>
15:29:31  #12 0x558636318c68 <unknown>
15:29:31  #13 0x5586362df60f <unknown>
15:29:31  #14 0x55863633d1f8 <unknown>
15:29:31  #15 0x55863633d3d6 <unknown>
15:29:31  #16 0x55863634e5e6 <unknown>
15:29:31  #17 0x7fd685430609 start_thread
15:29:31  
15:29:31  
15:29:31  10 tests, 0 passed, 10 failed
15:29:31  ==============================================================================
15:29:31  Test.Create Question                                                          
15:29:31  ==============================================================================
15:29:31  VERIFY FORM CREATE QUESTION IS DISPLAYED :: Test verify form creat... | FAIL |
15:29:31  Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x56217ca752ca <unknown>
15:29:31  #1 0x56217c51c550 <unknown>
15:29:31  #2 0x56217c5569cb <unknown>
15:29:31  #3 0x56217c551d17 <unknown>
15:29:31  #4 0x56217c5a219e <unknown>
15:29:31  #5 0x56217c5a1766 <unknown>
15:29:31  #6 0x56217c593993 <unknown>
15:29:31  #7 0x56217c55fd6b <unknown>
15:29:31  #8 0x56217c561141 <unknown>
15:29:31  #9 0x56217ca3a2ab <unknown>
15:29:31  #10 0x56217ca3e0b9 <unknown>
15:29:31  #11 0x56217ca21139 <unknown>
15:29:31  #12 0x56217ca3ec68 <unknown>
15:29:31  #13 0x56217ca0560f <unknown>
15:29:31  #14 0x56217ca631f8 <unknown>
15:29:31  #15 0x56217ca633d6 <unknown>
15:29:31  #16 0x56217ca745e6 <unknown>
15:29:31  #17 0x7f4466658609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  VERIFY DROPDOWN OPTION CREATE QUESTION FROM :: Test verify dropdow... | FAIL |
15:29:31  Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x56109b7d32ca <unknown>
15:29:31  #1 0x56109b27a550 <unknown>
15:29:31  #2 0x56109b2b49cb <unknown>
15:29:31  #3 0x56109b2afd17 <unknown>
15:29:31  #4 0x56109b30019e <unknown>
15:29:31  #5 0x56109b2ff766 <unknown>
15:29:31  #6 0x56109b2f1993 <unknown>
15:29:31  #7 0x56109b2bdd6b <unknown>
15:29:31  #8 0x56109b2bf141 <unknown>
15:29:31  #9 0x56109b7982ab <unknown>
15:29:31  #10 0x56109b79c0b9 <unknown>
15:29:31  #11 0x56109b77f139 <unknown>
15:29:31  #12 0x56109b79cc68 <unknown>
15:29:31  #13 0x56109b76360f <unknown>
15:29:31  #14 0x56109b7c11f8 <unknown>
15:29:31  #15 0x56109b7c13d6 <unknown>
15:29:31  #16 0x56109b7d25e6 <unknown>
15:29:31  #17 0x7fbd34db6609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  VERIFY REQUIRED FIELD ERRORS :: Test verify required field error m... | FAIL |
15:29:31  Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55b4976492ca <unknown>
15:29:31  #1 0x55b4970f0550 <unknown>
15:29:31  #2 0x55b49712a9cb <unknown>
15:29:31  #3 0x55b497125d17 <unknown>
15:29:31  #4 0x55b49717619e <unknown>
15:29:31  #5 0x55b497175766 <unknown>
15:29:31  #6 0x55b497167993 <unknown>
15:29:31  #7 0x55b497133d6b <unknown>
15:29:31  #8 0x55b497135141 <unknown>
15:29:31  #9 0x55b49760e2ab <unknown>
15:29:31  #10 0x55b4976120b9 <unknown>
15:29:31  #11 0x55b4975f5139 <unknown>
15:29:31  #12 0x55b497612c68 <unknown>
15:29:31  #13 0x55b4975d960f <unknown>
15:29:31  #14 0x55b4976371f8 <unknown>
15:29:31  #15 0x55b4976373d6 <unknown>
15:29:31  #16 0x55b4976485e6 <unknown>
15:29:31  #17 0x7f306f5d8609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  VERIFY ANSWER FORM :: Test verify answer form appears when Multipl... | FAIL |
15:29:31  Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55c36d3542ca <unknown>
15:29:31  #1 0x55c36cdfb550 <unknown>
15:29:31  #2 0x55c36ce359cb <unknown>
15:29:31  #3 0x55c36ce30d17 <unknown>
15:29:31  #4 0x55c36ce8119e <unknown>
15:29:31  #5 0x55c36ce80766 <unknown>
15:29:31  #6 0x55c36ce72993 <unknown>
15:29:31  #7 0x55c36ce3ed6b <unknown>
15:29:31  #8 0x55c36ce40141 <unknown>
15:29:31  #9 0x55c36d3192ab <unknown>
15:29:31  #10 0x55c36d31d0b9 <unknown>
15:29:31  #11 0x55c36d300139 <unknown>
15:29:31  #12 0x55c36d31dc68 <unknown>
15:29:31  #13 0x55c36d2e460f <unknown>
15:29:31  #14 0x55c36d3421f8 <unknown>
15:29:31  #15 0x55c36d3423d6 <unknown>
15:29:31  #16 0x55c36d3535e6 <unknown>
15:29:31  #17 0x7fcf272d3609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  CREATE SIMPLE QUESTION FORM :: Test create a simple question          | FAIL |
15:29:31  Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x5636f614e2ca <unknown>
15:29:31  #1 0x5636f5bf5550 <unknown>
15:29:31  #2 0x5636f5c2f9cb <unknown>
15:29:31  #3 0x5636f5c2ad17 <unknown>
15:29:31  #4 0x5636f5c7b19e <unknown>
15:29:31  #5 0x5636f5c7a766 <unknown>
15:29:31  #6 0x5636f5c6c993 <unknown>
15:29:31  #7 0x5636f5c38d6b <unknown>
15:29:31  #8 0x5636f5c3a141 <unknown>
15:29:31  #9 0x5636f61132ab <unknown>
15:29:31  #10 0x5636f61170b9 <unknown>
15:29:31  #11 0x5636f60fa139 <unknown>
15:29:31  #12 0x5636f6117c68 <unknown>
15:29:31  #13 0x5636f60de60f <unknown>
15:29:31  #14 0x5636f613c1f8 <unknown>
15:29:31  #15 0x5636f613c3d6 <unknown>
15:29:31  #16 0x5636f614d5e6 <unknown>
15:29:31  #17 0x7f5c82f2b609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Test.Create Question                                                  | FAIL |
15:29:31  5 tests, 0 passed, 5 failed
15:29:31  ==============================================================================
15:29:31  Test.Login Tests                                                              
15:29:31  ==============================================================================
15:29:31  Verify Login With User Admin :: Test case to verify valid login wi... | FAIL |
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x559ddda962ca <unknown>
15:29:31  #1 0x559ddd53d550 <unknown>
15:29:31  #2 0x559ddd5779cb <unknown>
15:29:31  #3 0x559ddd572d17 <unknown>
15:29:31  #4 0x559ddd5c319e <unknown>
15:29:31  #5 0x559ddd5c2766 <unknown>
15:29:31  #6 0x559ddd5b4993 <unknown>
15:29:31  #7 0x559ddd580d6b <unknown>
15:29:31  #8 0x559ddd582141 <unknown>
15:29:31  #9 0x559ddda5b2ab <unknown>
15:29:31  #10 0x559ddda5f0b9 <unknown>
15:29:31  #11 0x559ddda42139 <unknown>
15:29:31  #12 0x559ddda5fc68 <unknown>
15:29:31  #13 0x559ddda2660f <unknown>
15:29:31  #14 0x559ddda841f8 <unknown>
15:29:31  #15 0x559ddda843d6 <unknown>
15:29:31  #16 0x559ddda955e6 <unknown>
15:29:31  #17 0x7faf257e2609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Verify Login With User Lecturer :: Test case to verify valid login... | FAIL |
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x5586b94ca2ca <unknown>
15:29:31  #1 0x5586b8f71550 <unknown>
15:29:31  #2 0x5586b8fab9cb <unknown>
15:29:31  #3 0x5586b8fa6d17 <unknown>
15:29:31  #4 0x5586b8ff719e <unknown>
15:29:31  #5 0x5586b8ff6766 <unknown>
15:29:31  #6 0x5586b8fe8993 <unknown>
15:29:31  #7 0x5586b8fb4d6b <unknown>
15:29:31  #8 0x5586b8fb6141 <unknown>
15:29:31  #9 0x5586b948f2ab <unknown>
15:29:31  #10 0x5586b94930b9 <unknown>
15:29:31  #11 0x5586b9476139 <unknown>
15:29:31  #12 0x5586b9493c68 <unknown>
15:29:31  #13 0x5586b945a60f <unknown>
15:29:31  #14 0x5586b94b81f8 <unknown>
15:29:31  #15 0x5586b94b83d6 <unknown>
15:29:31  #16 0x5586b94c95e6 <unknown>
15:29:31  #17 0x7fb4588aa609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Verify Login With Invalid User :: Test case to verify invalid login   | FAIL |
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55b2c70bc2ca <unknown>
15:29:31  #1 0x55b2c6b63550 <unknown>
15:29:31  #2 0x55b2c6b9d9cb <unknown>
15:29:31  #3 0x55b2c6b98d17 <unknown>
15:29:31  #4 0x55b2c6be919e <unknown>
15:29:31  #5 0x55b2c6be8766 <unknown>
15:29:31  #6 0x55b2c6bda993 <unknown>
15:29:31  #7 0x55b2c6ba6d6b <unknown>
15:29:31  #8 0x55b2c6ba8141 <unknown>
15:29:31  #9 0x55b2c70812ab <unknown>
15:29:31  #10 0x55b2c70850b9 <unknown>
15:29:31  #11 0x55b2c7068139 <unknown>
15:29:31  #12 0x55b2c7085c68 <unknown>
15:29:31  #13 0x55b2c704c60f <unknown>
15:29:31  #14 0x55b2c70aa1f8 <unknown>
15:29:31  #15 0x55b2c70aa3d6 <unknown>
15:29:31  #16 0x55b2c70bb5e6 <unknown>
15:29:31  #17 0x7f2476ee6609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Verify Login With Multiple Accounts From Excel :: Kiểm tra đăng nh... | FAIL |
15:29:31  Variable '@{ROBOT_USERS_PANDAS}' not found.
15:29:31  ------------------------------------------------------------------------------
15:29:31  Test.Login Tests                                                      | FAIL |
15:29:31  4 tests, 0 passed, 4 failed
15:29:31  ==============================================================================
15:29:31  Test.Program Test :: Page Object in Robot Framework                           
15:29:31  ==============================================================================
15:29:31  Verify successful create new program :: This test case verifies cr... | FAIL |
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55fb7a2dc2ca <unknown>
15:29:31  #1 0x55fb79d83550 <unknown>
15:29:31  #2 0x55fb79dbd9cb <unknown>
15:29:31  #3 0x55fb79db8d17 <unknown>
15:29:31  #4 0x55fb79e0919e <unknown>
15:29:31  #5 0x55fb79e08766 <unknown>
15:29:31  #6 0x55fb79dfa993 <unknown>
15:29:31  #7 0x55fb79dc6d6b <unknown>
15:29:31  #8 0x55fb79dc8141 <unknown>
15:29:31  #9 0x55fb7a2a12ab <unknown>
15:29:31  #10 0x55fb7a2a50b9 <unknown>
15:29:31  #11 0x55fb7a288139 <unknown>
15:29:31  #12 0x55fb7a2a5c68 <unknown>
15:29:31  #13 0x55fb7a26c60f <unknown>
15:29:31  #14 0x55fb7a2ca1f8 <unknown>
15:29:31  #15 0x55fb7a2ca3d6 <unknown>
15:29:31  #16 0x55fb7a2db5e6 <unknown>
15:29:31  #17 0x7ffafff6e609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Verify create program fails when all fields are empty :: This test... | FAIL |
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x556a6f28b2ca <unknown>
15:29:31  #1 0x556a6ed32550 <unknown>
15:29:31  #2 0x556a6ed6c9cb <unknown>
15:29:31  #3 0x556a6ed67d17 <unknown>
15:29:31  #4 0x556a6edb819e <unknown>
15:29:31  #5 0x556a6edb7766 <unknown>
15:29:31  #6 0x556a6eda9993 <unknown>
15:29:31  #7 0x556a6ed75d6b <unknown>
15:29:31  #8 0x556a6ed77141 <unknown>
15:29:31  #9 0x556a6f2502ab <unknown>
15:29:31  #10 0x556a6f2540b9 <unknown>
15:29:31  #11 0x556a6f237139 <unknown>
15:29:31  #12 0x556a6f254c68 <unknown>
15:29:31  #13 0x556a6f21b60f <unknown>
15:29:31  #14 0x556a6f2791f8 <unknown>
15:29:31  #15 0x556a6f2793d6 <unknown>
15:29:31  #16 0x556a6f28a5e6 <unknown>
15:29:31  #17 0x7fb73cc5e609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Verify go to Program page :: Verify go to Program page                | FAIL |
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55de612392ca <unknown>
15:29:31  #1 0x55de60ce0550 <unknown>
15:29:31  #2 0x55de60d1a9cb <unknown>
15:29:31  #3 0x55de60d15d17 <unknown>
15:29:31  #4 0x55de60d6619e <unknown>
15:29:31  #5 0x55de60d65766 <unknown>
15:29:31  #6 0x55de60d57993 <unknown>
15:29:31  #7 0x55de60d23d6b <unknown>
15:29:31  #8 0x55de60d25141 <unknown>
15:29:31  #9 0x55de611fe2ab <unknown>
15:29:31  #10 0x55de612020b9 <unknown>
15:29:31  #11 0x55de611e5139 <unknown>
15:29:31  #12 0x55de61202c68 <unknown>
15:29:31  #13 0x55de611c960f <unknown>
15:29:31  #14 0x55de612271f8 <unknown>
15:29:31  #15 0x55de612273d6 <unknown>
15:29:31  #16 0x55de612385e6 <unknown>
15:29:31  #17 0x7f3777eef609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Search for program by Name :: Verify that the program search retur... | FAIL |
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x557b061b02ca <unknown>
15:29:31  #1 0x557b05c57550 <unknown>
15:29:31  #2 0x557b05c919cb <unknown>
15:29:31  #3 0x557b05c8cd17 <unknown>
15:29:31  #4 0x557b05cdd19e <unknown>
15:29:31  #5 0x557b05cdc766 <unknown>
15:29:31  #6 0x557b05cce993 <unknown>
15:29:31  #7 0x557b05c9ad6b <unknown>
15:29:31  #8 0x557b05c9c141 <unknown>
15:29:31  #9 0x557b061752ab <unknown>
15:29:31  #10 0x557b061790b9 <unknown>
15:29:31  #11 0x557b0615c139 <unknown>
15:29:31  #12 0x557b06179c68 <unknown>
15:29:31  #13 0x557b0614060f <unknown>
15:29:31  #14 0x557b0619e1f8 <unknown>
15:29:31  #15 0x557b0619e3d6 <unknown>
15:29:31  #16 0x557b061af5e6 <unknown>
15:29:31  #17 0x7fb046b17609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Search for program not found :: Verify that the program search ret... | FAIL |
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x56327549d2ca <unknown>
15:29:31  #1 0x563274f44550 <unknown>
15:29:31  #2 0x563274f7e9cb <unknown>
15:29:31  #3 0x563274f79d17 <unknown>
15:29:31  #4 0x563274fca19e <unknown>
15:29:31  #5 0x563274fc9766 <unknown>
15:29:31  #6 0x563274fbb993 <unknown>
15:29:31  #7 0x563274f87d6b <unknown>
15:29:31  #8 0x563274f89141 <unknown>
15:29:31  #9 0x5632754622ab <unknown>
15:29:31  #10 0x5632754660b9 <unknown>
15:29:31  #11 0x563275449139 <unknown>
15:29:31  #12 0x563275466c68 <unknown>
15:29:31  #13 0x56327542d60f <unknown>
15:29:31  #14 0x56327548b1f8 <unknown>
15:29:31  #15 0x56327548b3d6 <unknown>
15:29:31  #16 0x56327549c5e6 <unknown>
15:29:31  #17 0x7fc1035bd609 start_thread
15:29:31  ------------------------------------------------------------------------------
15:29:31  Test.Program Test :: Page Object in Robot Framework                   | FAIL |
15:29:31  5 tests, 0 passed, 5 failed
15:29:31  ==============================================================================
15:29:31  Test                                                                  | FAIL |
15:29:31  34 tests, 0 passed, 34 failed
15:29:31  ==============================================================================
15:29:31  Output:  /var/lib/jenkins/workspace/v-job/results/output.xml
15:29:31  Log:     /var/lib/jenkins/workspace/v-job/results/log.html
15:29:31  Report:  /var/lib/jenkins/workspace/v-job/results/report.html
15:29:31  
15:29:31  STDERR:
15:29:31  [ ERROR ] Error in file '/var/lib/jenkins/workspace/v-job/test/chapter_test.robot' on line 12: Processing variable file '/var/lib/jenkins/workspace/v-job/libs/data_reader.py' failed: Importing variable file '/var/lib/jenkins/workspace/v-job/libs/data_reader.py' failed: ModuleNotFoundError: No module named 'pandas'
15:29:31  Traceback (most recent call last):
15:29:31    File "/var/lib/jenkins/workspace/v-job/libs/data_reader.py", line 2, in <module>
15:29:31      import pandas as pd
15:29:31  PYTHONPATH:
15:29:31    /var/lib/jenkins/workspace/v-job/libs
15:29:31    /var/lib/jenkins/.local/bin
15:29:31    /usr/lib/python38.zip
15:29:31    /usr/lib/python3.8
15:29:31    /usr/lib/python3.8/lib-dynload
15:29:31    /var/lib/jenkins/.local/lib/python3.8/site-packages
15:29:31    /usr/local/lib/python3.8/dist-packages
15:29:31    /usr/lib/python3/dist-packages
15:29:31  [ ERROR ] Error in file '/var/lib/jenkins/workspace/v-job/test/course_test.robot' on line 14: Processing variable file '/var/lib/jenkins/workspace/v-job/libs/data_reader.py' failed: Importing variable file '/var/lib/jenkins/workspace/v-job/libs/data_reader.py' failed: ModuleNotFoundError: No module named 'pandas'
15:29:31  Traceback (most recent call last):
15:29:31    File "/var/lib/jenkins/workspace/v-job/libs/data_reader.py", line 2, in <module>
15:29:31      import pandas as pd
15:29:31  PYTHONPATH:
15:29:31    /var/lib/jenkins/workspace/v-job/libs
15:29:31    /var/lib/jenkins/.local/bin
15:29:31    /usr/lib/python38.zip
15:29:31    /usr/lib/python3.8
15:29:31    /usr/lib/python3.8/lib-dynload
15:29:31    /var/lib/jenkins/.local/lib/python3.8/site-packages
15:29:31    /usr/local/lib/python3.8/dist-packages
15:29:31    /usr/lib/python3/dist-packages
15:29:31  [ ERROR ] Error in file '/var/lib/jenkins/workspace/v-job/test/login_tests.robot' on line 6: Processing variable file '/var/lib/jenkins/workspace/v-job/libs/data_reader.py' failed: Importing variable file '/var/lib/jenkins/workspace/v-job/libs/data_reader.py' failed: ModuleNotFoundError: No module named 'pandas'
15:29:31  Traceback (most recent call last):
15:29:31    File "/var/lib/jenkins/workspace/v-job/libs/data_reader.py", line 2, in <module>
15:29:31      import pandas as pd
15:29:31  PYTHONPATH:
15:29:31    /var/lib/jenkins/workspace/v-job/libs
15:29:31    /var/lib/jenkins/.local/bin
15:29:31    /usr/lib/python38.zip
15:29:31    /usr/lib/python3.8
15:29:31    /usr/lib/python3.8/lib-dynload
15:29:31    /var/lib/jenkins/.local/lib/python3.8/site-packages
15:29:31    /usr/local/lib/python3.8/dist-packages
15:29:31    /usr/lib/python3/dist-packages
15:29:31  
15:29:31  Collecting test results...
15:29:31  Test Results Summary:
15:29:31    Total: 34
15:29:31    Passed: 0
15:29:31    Failed: 34
15:29:31    Success Rate: 0.00%
15:29:31    Overall Status: FAIL
15:29:31    Duration: N/A
15:29:31  
15:29:31  Failed Tests:
15:29:31    - Verify Form Add Assessment is Displayed: Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x5587154412ca <unknown>
15:29:31  #1 0x558714ee8550 <unknown>
15:29:31  #2 0x558714f229cb <unknown>
15:29:31  #3 0x558714f1dd17 <unknown>
15:29:31  #4 0x558714f6e19e <unknown>
15:29:31  #5 0x558714f6d766 <unknown>
15:29:31  #6 0x558714f5f993 <unknown>
15:29:31  #7 0x558714f2bd6b <unknown>
15:29:31  #8 0x558714f2d141 <unknown>
15:29:31  #9 0x5587154062ab <unknown>
15:29:31  #10 0x55871540a0b9 <unknown>
15:29:31  #11 0x5587153ed139 <unknown>
15:29:31  #12 0x55871540ac68 <unknown>
15:29:31  #13 0x5587153d160f <unknown>
15:29:31  #14 0x55871542f1f8 <unknown>
15:29:31  #15 0x55871542f3d6 <unknown>
15:29:31  #16 0x5587154405e6 <unknown>
15:29:31  #17 0x7fabd9963609 start_thread
15:29:31  
15:29:31    - Verify Add Assessment Successfully: Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x5626e79dc2ca <unknown>
15:29:31  #1 0x5626e7483550 <unknown>
15:29:31  #2 0x5626e74bd9cb <unknown>
15:29:31  #3 0x5626e74b8d17 <unknown>
15:29:31  #4 0x5626e750919e <unknown>
15:29:31  #5 0x5626e7508766 <unknown>
15:29:31  #6 0x5626e74fa993 <unknown>
15:29:31  #7 0x5626e74c6d6b <unknown>
15:29:31  #8 0x5626e74c8141 <unknown>
15:29:31  #9 0x5626e79a12ab <unknown>
15:29:31  #10 0x5626e79a50b9 <unknown>
15:29:31  #11 0x5626e7988139 <unknown>
15:29:31  #12 0x5626e79a5c68 <unknown>
15:29:31  #13 0x5626e796c60f <unknown>
15:29:31  #14 0x5626e79ca1f8 <unknown>
15:29:31  #15 0x5626e79ca3d6 <unknown>
15:29:31  #16 0x5626e79db5e6 <unknown>
15:29:31  #17 0x7f3d97472609 start_thread
15:29:31  
15:29:31    - Verify Leave Required Fields Empty: Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x556cb82722ca <unknown>
15:29:31  #1 0x556cb7d19550 <unknown>
15:29:31  #2 0x556cb7d539cb <unknown>
15:29:31  #3 0x556cb7d4ed17 <unknown>
15:29:31  #4 0x556cb7d9f19e <unknown>
15:29:31  #5 0x556cb7d9e766 <unknown>
15:29:31  #6 0x556cb7d90993 <unknown>
15:29:31  #7 0x556cb7d5cd6b <unknown>
15:29:31  #8 0x556cb7d5e141 <unknown>
15:29:31  #9 0x556cb82372ab <unknown>
15:29:31  #10 0x556cb823b0b9 <unknown>
15:29:31  #11 0x556cb821e139 <unknown>
15:29:31  #12 0x556cb823bc68 <unknown>
15:29:31  #13 0x556cb820260f <unknown>
15:29:31  #14 0x556cb82601f8 <unknown>
15:29:31  #15 0x556cb82603d6 <unknown>
15:29:31  #16 0x556cb82715e6 <unknown>
15:29:31  #17 0x7f435c70d609 start_thread
15:29:31  
15:29:31    - Verify Fill Assessment With HighWeight: Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x5588325b42ca <unknown>
15:29:31  #1 0x55883205b550 <unknown>
15:29:31  #2 0x5588320959cb <unknown>
15:29:31  #3 0x558832090d17 <unknown>
15:29:31  #4 0x5588320e119e <unknown>
15:29:31  #5 0x5588320e0766 <unknown>
15:29:31  #6 0x5588320d2993 <unknown>
15:29:31  #7 0x55883209ed6b <unknown>
15:29:31  #8 0x5588320a0141 <unknown>
15:29:31  #9 0x5588325792ab <unknown>
15:29:31  #10 0x55883257d0b9 <unknown>
15:29:31  #11 0x558832560139 <unknown>
15:29:31  #12 0x55883257dc68 <unknown>
15:29:31  #13 0x55883254460f <unknown>
15:29:31  #14 0x5588325a21f8 <unknown>
15:29:31  #15 0x5588325a23d6 <unknown>
15:29:31  #16 0x5588325b35e6 <unknown>
15:29:31  #17 0x7fce95e78609 start_thread
15:29:31  
15:29:31    - Verify Fill Assessment WithInvalidPassScore: Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x558b34cd32ca <unknown>
15:29:31  #1 0x558b3477a550 <unknown>
15:29:31  #2 0x558b347b49cb <unknown>
15:29:31  #3 0x558b347afd17 <unknown>
15:29:31  #4 0x558b3480019e <unknown>
15:29:31  #5 0x558b347ff766 <unknown>
15:29:31  #6 0x558b347f1993 <unknown>
15:29:31  #7 0x558b347bdd6b <unknown>
15:29:31  #8 0x558b347bf141 <unknown>
15:29:31  #9 0x558b34c982ab <unknown>
15:29:31  #10 0x558b34c9c0b9 <unknown>
15:29:31  #11 0x558b34c7f139 <unknown>
15:29:31  #12 0x558b34c9cc68 <unknown>
15:29:31  #13 0x558b34c6360f <unknown>
15:29:31  #14 0x558b34cc11f8 <unknown>
15:29:31  #15 0x558b34cc13d6 <unknown>
15:29:31  #16 0x558b34cd25e6 <unknown>
15:29:31  #17 0x7f742a659609 start_thread
15:29:31  
15:29:31    - Verify Fill Assessment WithSpecialDescription: Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x56214acd62ca <unknown>
15:29:31  #1 0x56214a77d550 <unknown>
15:29:31  #2 0x56214a7b79cb <unknown>
15:29:31  #3 0x56214a7b2d17 <unknown>
15:29:31  #4 0x56214a80319e <unknown>
15:29:31  #5 0x56214a802766 <unknown>
15:29:31  #6 0x56214a7f4993 <unknown>
15:29:31  #7 0x56214a7c0d6b <unknown>
15:29:31  #8 0x56214a7c2141 <unknown>
15:29:31  #9 0x56214ac9b2ab <unknown>
15:29:31  #10 0x56214ac9f0b9 <unknown>
15:29:31  #11 0x56214ac82139 <unknown>
15:29:31  #12 0x56214ac9fc68 <unknown>
15:29:31  #13 0x56214ac6660f <unknown>
15:29:31  #14 0x56214acc41f8 <unknown>
15:29:31  #15 0x56214acc43d6 <unknown>
15:29:31  #16 0x56214acd55e6 <unknown>
15:29:31  #17 0x7f4718645609 start_thread
15:29:31  
15:29:31    - TC_BrowserQs_001: Approve Question: Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55589fec82ca <unknown>
15:29:31  #1 0x55589f96f550 <unknown>
15:29:31  #2 0x55589f9a99cb <unknown>
15:29:31  #3 0x55589f9a4d17 <unknown>
15:29:31  #4 0x55589f9f519e <unknown>
15:29:31  #5 0x55589f9f4766 <unknown>
15:29:31  #6 0x55589f9e6993 <unknown>
15:29:31  #7 0x55589f9b2d6b <unknown>
15:29:31  #8 0x55589f9b4141 <unknown>
15:29:31  #9 0x55589fe8d2ab <unknown>
15:29:31  #10 0x55589fe910b9 <unknown>
15:29:31  #11 0x55589fe74139 <unknown>
15:29:31  #12 0x55589fe91c68 <unknown>
15:29:31  #13 0x55589fe5860f <unknown>
15:29:31  #14 0x55589feb61f8 <unknown>
15:29:31  #15 0x55589feb63d6 <unknown>
15:29:31  #16 0x55589fec75e6 <unknown>
15:29:31  #17 0x7ffa4bc7e609 start_thread
15:29:31  
15:29:31    - TC_BrowserQs_002: Reject Question: Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55d2776da2ca <unknown>
15:29:31  #1 0x55d277181550 <unknown>
15:29:31  #2 0x55d2771bb9cb <unknown>
15:29:31  #3 0x55d2771b6d17 <unknown>
15:29:31  #4 0x55d27720719e <unknown>
15:29:31  #5 0x55d277206766 <unknown>
15:29:31  #6 0x55d2771f8993 <unknown>
15:29:31  #7 0x55d2771c4d6b <unknown>
15:29:31  #8 0x55d2771c6141 <unknown>
15:29:31  #9 0x55d27769f2ab <unknown>
15:29:31  #10 0x55d2776a30b9 <unknown>
15:29:31  #11 0x55d277686139 <unknown>
15:29:31  #12 0x55d2776a3c68 <unknown>
15:29:31  #13 0x55d27766a60f <unknown>
15:29:31  #14 0x55d2776c81f8 <unknown>
15:29:31  #15 0x55d2776c83d6 <unknown>
15:29:31  #16 0x55d2776d95e6 <unknown>
15:29:31  #17 0x7f09bd703609 start_thread
15:29:31  
15:29:31    - TC_BrowserQs_003: Filter Course: Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55a68efe92ca <unknown>
15:29:31  #1 0x55a68ea90550 <unknown>
15:29:31  #2 0x55a68eaca9cb <unknown>
15:29:31  #3 0x55a68eac5d17 <unknown>
15:29:31  #4 0x55a68eb1619e <unknown>
15:29:31  #5 0x55a68eb15766 <unknown>
15:29:31  #6 0x55a68eb07993 <unknown>
15:29:31  #7 0x55a68ead3d6b <unknown>
15:29:31  #8 0x55a68ead5141 <unknown>
15:29:31  #9 0x55a68efae2ab <unknown>
15:29:31  #10 0x55a68efb20b9 <unknown>
15:29:31  #11 0x55a68ef95139 <unknown>
15:29:31  #12 0x55a68efb2c68 <unknown>
15:29:31  #13 0x55a68ef7960f <unknown>
15:29:31  #14 0x55a68efd71f8 <unknown>
15:29:31  #15 0x55a68efd73d6 <unknown>
15:29:31  #16 0x55a68efe85e6 <unknown>
15:29:31  #17 0x7f2a2bf79609 start_thread
15:29:31  
15:29:31    - Tcs 01: Func-Chapter: Create Chapter with fully field: Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55d461e762ca <unknown>
15:29:31  #1 0x55d46191d550 <unknown>
15:29:31  #2 0x55d4619579cb <unknown>
15:29:31  #3 0x55d461952d17 <unknown>
15:29:31  #4 0x55d4619a319e <unknown>
15:29:31  #5 0x55d4619a2766 <unknown>
15:29:31  #6 0x55d461994993 <unknown>
15:29:31  #7 0x55d461960d6b <unknown>
15:29:31  #8 0x55d461962141 <unknown>
15:29:31  #9 0x55d461e3b2ab <unknown>
15:29:31  #10 0x55d461e3f0b9 <unknown>
15:29:31  #11 0x55d461e22139 <unknown>
15:29:31  #12 0x55d461e3fc68 <unknown>
15:29:31  #13 0x55d461e0660f <unknown>
15:29:31  #14 0x55d461e641f8 <unknown>
15:29:31  #15 0x55d461e643d6 <unknown>
15:29:31  #16 0x55d461e755e6 <unknown>
15:29:31  #17 0x7faaa9cdd609 start_thread
15:29:31  
15:29:31    - Tcs 01: Func-Course-03: Create Course with fully field: Parent suite setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55863634f2ca <unknown>
15:29:31  #1 0x558635df6550 <unknown>
15:29:31  #2 0x558635e309cb <unknown>
15:29:31  #3 0x558635e2bd17 <unknown>
15:29:31  #4 0x558635e7c19e <unknown>
15:29:31  #5 0x558635e7b766 <unknown>
15:29:31  #6 0x558635e6d993 <unknown>
15:29:31  #7 0x558635e39d6b <unknown>
15:29:31  #8 0x558635e3b141 <unknown>
15:29:31  #9 0x5586363142ab <unknown>
15:29:31  #10 0x5586363180b9 <unknown>
15:29:31  #11 0x5586362fb139 <unknown>
15:29:31  #12 0x558636318c68 <unknown>
15:29:31  #13 0x5586362df60f <unknown>
15:29:31  #14 0x55863633d1f8 <unknown>
15:29:31  #15 0x55863633d3d6 <unknown>
15:29:31  #16 0x55863634e5e6 <unknown>
15:29:31  #17 0x7fd685430609 start_thread
15:29:31  
15:29:31    - Tcs 02: Func-Course-04: Create course with empty required fields: Parent suite setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55863634f2ca <unknown>
15:29:31  #1 0x558635df6550 <unknown>
15:29:31  #2 0x558635e309cb <unknown>
15:29:31  #3 0x558635e2bd17 <unknown>
15:29:31  #4 0x558635e7c19e <unknown>
15:29:31  #5 0x558635e7b766 <unknown>
15:29:31  #6 0x558635e6d993 <unknown>
15:29:31  #7 0x558635e39d6b <unknown>
15:29:31  #8 0x558635e3b141 <unknown>
15:29:31  #9 0x5586363142ab <unknown>
15:29:31  #10 0x5586363180b9 <unknown>
15:29:31  #11 0x5586362fb139 <unknown>
15:29:31  #12 0x558636318c68 <unknown>
15:29:31  #13 0x5586362df60f <unknown>
15:29:31  #14 0x55863633d1f8 <unknown>
15:29:31  #15 0x55863633d3d6 <unknown>
15:29:31  #16 0x55863634e5e6 <unknown>
15:29:31  #17 0x7fd685430609 start_thread
15:29:31  
15:29:31    - Tcs 11: Func-Course: Create Course with maximum credits value: Parent suite setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55863634f2ca <unknown>
15:29:31  #1 0x558635df6550 <unknown>
15:29:31  #2 0x558635e309cb <unknown>
15:29:31  #3 0x558635e2bd17 <unknown>
15:29:31  #4 0x558635e7c19e <unknown>
15:29:31  #5 0x558635e7b766 <unknown>
15:29:31  #6 0x558635e6d993 <unknown>
15:29:31  #7 0x558635e39d6b <unknown>
15:29:31  #8 0x558635e3b141 <unknown>
15:29:31  #9 0x5586363142ab <unknown>
15:29:31  #10 0x5586363180b9 <unknown>
15:29:31  #11 0x5586362fb139 <unknown>
15:29:31  #12 0x558636318c68 <unknown>
15:29:31  #13 0x5586362df60f <unknown>
15:29:31  #14 0x55863633d1f8 <unknown>
15:29:31  #15 0x55863633d3d6 <unknown>
15:29:31  #16 0x55863634e5e6 <unknown>
15:29:31  #17 0x7fd685430609 start_thread
15:29:31  
15:29:31    - Tcs 03: Func-CLO-03: Create CLOs with fully fields: Parent suite setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55863634f2ca <unknown>
15:29:31  #1 0x558635df6550 <unknown>
15:29:31  #2 0x558635e309cb <unknown>
15:29:31  #3 0x558635e2bd17 <unknown>
15:29:31  #4 0x558635e7c19e <unknown>
15:29:31  #5 0x558635e7b766 <unknown>
15:29:31  #6 0x558635e6d993 <unknown>
15:29:31  #7 0x558635e39d6b <unknown>
15:29:31  #8 0x558635e3b141 <unknown>
15:29:31  #9 0x5586363142ab <unknown>
15:29:31  #10 0x5586363180b9 <unknown>
15:29:31  #11 0x5586362fb139 <unknown>
15:29:31  #12 0x558636318c68 <unknown>
15:29:31  #13 0x5586362df60f <unknown>
15:29:31  #14 0x55863633d1f8 <unknown>
15:29:31  #15 0x55863633d3d6 <unknown>
15:29:31  #16 0x55863634e5e6 <unknown>
15:29:31  #17 0x7fd685430609 start_thread
15:29:31  
15:29:31    - Tcs 04: Func-CLO-04: Create CLOS with empty required fields: Parent suite setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55863634f2ca <unknown>
15:29:31  #1 0x558635df6550 <unknown>
15:29:31  #2 0x558635e309cb <unknown>
15:29:31  #3 0x558635e2bd17 <unknown>
15:29:31  #4 0x558635e7c19e <unknown>
15:29:31  #5 0x558635e7b766 <unknown>
15:29:31  #6 0x558635e6d993 <unknown>
15:29:31  #7 0x558635e39d6b <unknown>
15:29:31  #8 0x558635e3b141 <unknown>
15:29:31  #9 0x5586363142ab <unknown>
15:29:31  #10 0x5586363180b9 <unknown>
15:29:31  #11 0x5586362fb139 <unknown>
15:29:31  #12 0x558636318c68 <unknown>
15:29:31  #13 0x5586362df60f <unknown>
15:29:31  #14 0x55863633d1f8 <unknown>
15:29:31  #15 0x55863633d3d6 <unknown>
15:29:31  #16 0x55863634e5e6 <unknown>
15:29:31  #17 0x7fd685430609 start_thread
15:29:31  
15:29:31    - Tcs 06: Edit Course: Parent suite setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55863634f2ca <unknown>
15:29:31  #1 0x558635df6550 <unknown>
15:29:31  #2 0x558635e309cb <unknown>
15:29:31  #3 0x558635e2bd17 <unknown>
15:29:31  #4 0x558635e7c19e <unknown>
15:29:31  #5 0x558635e7b766 <unknown>
15:29:31  #6 0x558635e6d993 <unknown>
15:29:31  #7 0x558635e39d6b <unknown>
15:29:31  #8 0x558635e3b141 <unknown>
15:29:31  #9 0x5586363142ab <unknown>
15:29:31  #10 0x5586363180b9 <unknown>
15:29:31  #11 0x5586362fb139 <unknown>
15:29:31  #12 0x558636318c68 <unknown>
15:29:31  #13 0x5586362df60f <unknown>
15:29:31  #14 0x55863633d1f8 <unknown>
15:29:31  #15 0x55863633d3d6 <unknown>
15:29:31  #16 0x55863634e5e6 <unknown>
15:29:31  #17 0x7fd685430609 start_thread
15:29:31  
15:29:31    - Tcs 08: Compare value CLO in course: Parent suite setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55863634f2ca <unknown>
15:29:31  #1 0x558635df6550 <unknown>
15:29:31  #2 0x558635e309cb <unknown>
15:29:31  #3 0x558635e2bd17 <unknown>
15:29:31  #4 0x558635e7c19e <unknown>
15:29:31  #5 0x558635e7b766 <unknown>
15:29:31  #6 0x558635e6d993 <unknown>
15:29:31  #7 0x558635e39d6b <unknown>
15:29:31  #8 0x558635e3b141 <unknown>
15:29:31  #9 0x5586363142ab <unknown>
15:29:31  #10 0x5586363180b9 <unknown>
15:29:31  #11 0x5586362fb139 <unknown>
15:29:31  #12 0x558636318c68 <unknown>
15:29:31  #13 0x5586362df60f <unknown>
15:29:31  #14 0x55863633d1f8 <unknown>
15:29:31  #15 0x55863633d3d6 <unknown>
15:29:31  #16 0x55863634e5e6 <unknown>
15:29:31  #17 0x7fd685430609 start_thread
15:29:31  
15:29:31    - TC 09: Edit All Fields In Course: Parent suite setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55863634f2ca <unknown>
15:29:31  #1 0x558635df6550 <unknown>
15:29:31  #2 0x558635e309cb <unknown>
15:29:31  #3 0x558635e2bd17 <unknown>
15:29:31  #4 0x558635e7c19e <unknown>
15:29:31  #5 0x558635e7b766 <unknown>
15:29:31  #6 0x558635e6d993 <unknown>
15:29:31  #7 0x558635e39d6b <unknown>
15:29:31  #8 0x558635e3b141 <unknown>
15:29:31  #9 0x5586363142ab <unknown>
15:29:31  #10 0x5586363180b9 <unknown>
15:29:31  #11 0x5586362fb139 <unknown>
15:29:31  #12 0x558636318c68 <unknown>
15:29:31  #13 0x5586362df60f <unknown>
15:29:31  #14 0x55863633d1f8 <unknown>
15:29:31  #15 0x55863633d3d6 <unknown>
15:29:31  #16 0x55863634e5e6 <unknown>
15:29:31  #17 0x7fd685430609 start_thread
15:29:31  
15:29:31    - Tcs 12: "Tạo mới" button inactive: Parent suite setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55863634f2ca <unknown>
15:29:31  #1 0x558635df6550 <unknown>
15:29:31  #2 0x558635e309cb <unknown>
15:29:31  #3 0x558635e2bd17 <unknown>
15:29:31  #4 0x558635e7c19e <unknown>
15:29:31  #5 0x558635e7b766 <unknown>
15:29:31  #6 0x558635e6d993 <unknown>
15:29:31  #7 0x558635e39d6b <unknown>
15:29:31  #8 0x558635e3b141 <unknown>
15:29:31  #9 0x5586363142ab <unknown>
15:29:31  #10 0x5586363180b9 <unknown>
15:29:31  #11 0x5586362fb139 <unknown>
15:29:31  #12 0x558636318c68 <unknown>
15:29:31  #13 0x5586362df60f <unknown>
15:29:31  #14 0x55863633d1f8 <unknown>
15:29:31  #15 0x55863633d3d6 <unknown>
15:29:31  #16 0x55863634e5e6 <unknown>
15:29:31  #17 0x7fd685430609 start_thread
15:29:31  
15:29:31    - Tc 13: Find word with valid and invalid Keyword: Parent suite setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55863634f2ca <unknown>
15:29:31  #1 0x558635df6550 <unknown>
15:29:31  #2 0x558635e309cb <unknown>
15:29:31  #3 0x558635e2bd17 <unknown>
15:29:31  #4 0x558635e7c19e <unknown>
15:29:31  #5 0x558635e7b766 <unknown>
15:29:31  #6 0x558635e6d993 <unknown>
15:29:31  #7 0x558635e39d6b <unknown>
15:29:31  #8 0x558635e3b141 <unknown>
15:29:31  #9 0x5586363142ab <unknown>
15:29:31  #10 0x5586363180b9 <unknown>
15:29:31  #11 0x5586362fb139 <unknown>
15:29:31  #12 0x558636318c68 <unknown>
15:29:31  #13 0x5586362df60f <unknown>
15:29:31  #14 0x55863633d1f8 <unknown>
15:29:31  #15 0x55863633d3d6 <unknown>
15:29:31  #16 0x55863634e5e6 <unknown>
15:29:31  #17 0x7fd685430609 start_thread
15:29:31  
15:29:31    - VERIFY FORM CREATE QUESTION IS DISPLAYED: Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x56217ca752ca <unknown>
15:29:31  #1 0x56217c51c550 <unknown>
15:29:31  #2 0x56217c5569cb <unknown>
15:29:31  #3 0x56217c551d17 <unknown>
15:29:31  #4 0x56217c5a219e <unknown>
15:29:31  #5 0x56217c5a1766 <unknown>
15:29:31  #6 0x56217c593993 <unknown>
15:29:31  #7 0x56217c55fd6b <unknown>
15:29:31  #8 0x56217c561141 <unknown>
15:29:31  #9 0x56217ca3a2ab <unknown>
15:29:31  #10 0x56217ca3e0b9 <unknown>
15:29:31  #11 0x56217ca21139 <unknown>
15:29:31  #12 0x56217ca3ec68 <unknown>
15:29:31  #13 0x56217ca0560f <unknown>
15:29:31  #14 0x56217ca631f8 <unknown>
15:29:31  #15 0x56217ca633d6 <unknown>
15:29:31  #16 0x56217ca745e6 <unknown>
15:29:31  #17 0x7f4466658609 start_thread
15:29:31  
15:29:31    - VERIFY DROPDOWN OPTION CREATE QUESTION FROM: Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x56109b7d32ca <unknown>
15:29:31  #1 0x56109b27a550 <unknown>
15:29:31  #2 0x56109b2b49cb <unknown>
15:29:31  #3 0x56109b2afd17 <unknown>
15:29:31  #4 0x56109b30019e <unknown>
15:29:31  #5 0x56109b2ff766 <unknown>
15:29:31  #6 0x56109b2f1993 <unknown>
15:29:31  #7 0x56109b2bdd6b <unknown>
15:29:31  #8 0x56109b2bf141 <unknown>
15:29:31  #9 0x56109b7982ab <unknown>
15:29:31  #10 0x56109b79c0b9 <unknown>
15:29:31  #11 0x56109b77f139 <unknown>
15:29:31  #12 0x56109b79cc68 <unknown>
15:29:31  #13 0x56109b76360f <unknown>
15:29:31  #14 0x56109b7c11f8 <unknown>
15:29:31  #15 0x56109b7c13d6 <unknown>
15:29:31  #16 0x56109b7d25e6 <unknown>
15:29:31  #17 0x7fbd34db6609 start_thread
15:29:31  
15:29:31    - VERIFY REQUIRED FIELD ERRORS: Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55b4976492ca <unknown>
15:29:31  #1 0x55b4970f0550 <unknown>
15:29:31  #2 0x55b49712a9cb <unknown>
15:29:31  #3 0x55b497125d17 <unknown>
15:29:31  #4 0x55b49717619e <unknown>
15:29:31  #5 0x55b497175766 <unknown>
15:29:31  #6 0x55b497167993 <unknown>
15:29:31  #7 0x55b497133d6b <unknown>
15:29:31  #8 0x55b497135141 <unknown>
15:29:31  #9 0x55b49760e2ab <unknown>
15:29:31  #10 0x55b4976120b9 <unknown>
15:29:31  #11 0x55b4975f5139 <unknown>
15:29:31  #12 0x55b497612c68 <unknown>
15:29:31  #13 0x55b4975d960f <unknown>
15:29:31  #14 0x55b4976371f8 <unknown>
15:29:31  #15 0x55b4976373d6 <unknown>
15:29:31  #16 0x55b4976485e6 <unknown>
15:29:31  #17 0x7f306f5d8609 start_thread
15:29:31  
15:29:31    - VERIFY ANSWER FORM: Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x55c36d3542ca <unknown>
15:29:31  #1 0x55c36cdfb550 <unknown>
15:29:31  #2 0x55c36ce359cb <unknown>
15:29:31  #3 0x55c36ce30d17 <unknown>
15:29:31  #4 0x55c36ce8119e <unknown>
15:29:31  #5 0x55c36ce80766 <unknown>
15:29:31  #6 0x55c36ce72993 <unknown>
15:29:31  #7 0x55c36ce3ed6b <unknown>
15:29:31  #8 0x55c36ce40141 <unknown>
15:29:31  #9 0x55c36d3192ab <unknown>
15:29:31  #10 0x55c36d31d0b9 <unknown>
15:29:31  #11 0x55c36d300139 <unknown>
15:29:31  #12 0x55c36d31dc68 <unknown>
15:29:31  #13 0x55c36d2e460f <unknown>
15:29:31  #14 0x55c36d3421f8 <unknown>
15:29:31  #15 0x55c36d3423d6 <unknown>
15:29:31  #16 0x55c36d3535e6 <unknown>
15:29:31  #17 0x7fcf272d3609 start_thread
15:29:31  
15:29:31    - CREATE SIMPLE QUESTION FORM: Setup failed:
15:29:31  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x5636f614e2ca <unknown>
15:29:31  #1 0x5636f5bf5550 <unknown>
15:29:31  #2 0x5636f5c2f9cb <unknown>
15:29:31  #3 0x5636f5c2ad17 <unknown>
15:29:31  #4 0x5636f5c7b19e <unknown>
15:29:31  #5 0x5636f5c7a766 <unknown>
15:29:31  #6 0x5636f5c6c993 <unknown>
15:29:31  #7 0x5636f5c38d6b <unknown>
15:29:31  #8 0x5636f5c3a141 <unknown>
15:29:31  #9 0x5636f61132ab <unknown>
15:29:31  #10 0x5636f61170b9 <unknown>
15:29:31  #11 0x5636f60fa139 <unknown>
15:29:31  #12 0x5636f6117c68 <unknown>
15:29:31  #13 0x5636f60de60f <unknown>
15:29:31  #14 0x5636f613c1f8 <unknown>
15:29:31  #15 0x5636f613c3d6 <unknown>
15:29:31  #16 0x5636f614d5e6 <unknown>
15:29:31  #17 0x7f5c82f2b609 start_thread
15:29:31  
15:29:31    - Verify Login With User Admin: SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x559ddda962ca <unknown>
15:29:31  #1 0x559ddd53d550 <unknown>
15:29:31  #2 0x559ddd5779cb <unknown>
15:29:31  #3 0x559ddd572d17 <unknown>
15:29:31  #4 0x559ddd5c319e <unknown>
15:29:31  #5 0x559ddd5c2766 <unknown>
15:29:31  #6 0x559ddd5b4993 <unknown>
15:29:31  #7 0x559ddd580d6b <unknown>
15:29:31  #8 0x559ddd582141 <unknown>
15:29:31  #9 0x559ddda5b2ab <unknown>
15:29:31  #10 0x559ddda5f0b9 <unknown>
15:29:31  #11 0x559ddda42139 <unknown>
15:29:31  #12 0x559ddda5fc68 <unknown>
15:29:31  #13 0x559ddda2660f <unknown>
15:29:31  #14 0x559ddda841f8 <unknown>
15:29:31  #15 0x559ddda843d6 <unknown>
15:29:31  #16 0x559ddda955e6 <unknown>
15:29:31  #17 0x7faf257e2609 start_thread
15:29:31  
15:29:31    - Verify Login With User Lecturer: SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:31  Stacktrace:
15:29:31  #0 0x5586b94ca2ca <unknown>
15:29:31  #1 0x5586b8f71550 <unknown>
15:29:31  #2 0x5586b8fab9cb <unknown>
15:29:31  #3 0x5586b8fa6d17 <unknown>
15:29:31  #4 0x5586b8ff719e <unknown>
15:29:31  #5 0x5586b8ff6766 <unknown>
15:29:31  #6 0x5586b8fe8993 <unknown>
15:29:31  #7 0x5586b8fb4d6b <unknown>
15:29:31  #8 0x5586b8fb6141 <unknown>
15:29:31  #9 0x5586b948f2ab <unknown>
15:29:31  #10 0x5586b94930b9 <unknown>
15:29:31  #11 0x5586b9476139 <unknown>
15:29:31  #12 0x5586b9493c68 <unknown>
15:29:31  #13 0x5586b945a60f <unknown>
15:29:31  #14 0x5586b94b81f8 <unknown>
15:29:31  #15 0x5586b94b83d6 <unknown>
15:29:31  #16 0x5586b94c95e6 <unknown>
15:29:31  #17 0x7fb4588aa609 start_thread
15:29:31  
15:29:41    - Verify Login With Invalid User: SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:41  Stacktrace:
15:29:41  #0 0x55b2c70bc2ca <unknown>
15:29:41  #1 0x55b2c6b63550 <unknown>
15:29:41  #2 0x55b2c6b9d9cb <unknown>
15:29:41  #3 0x55b2c6b98d17 <unknown>
15:29:41  #4 0x55b2c6be919e <unknown>
15:29:41  #5 0x55b2c6be8766 <unknown>
15:29:41  #6 0x55b2c6bda993 <unknown>
15:29:41  #7 0x55b2c6ba6d6b <unknown>
15:29:41  #8 0x55b2c6ba8141 <unknown>
15:29:41  #9 0x55b2c70812ab <unknown>
15:29:41  #10 0x55b2c70850b9 <unknown>
15:29:41  #11 0x55b2c7068139 <unknown>
15:29:41  #12 0x55b2c7085c68 <unknown>
15:29:41  #13 0x55b2c704c60f <unknown>
15:29:41  #14 0x55b2c70aa1f8 <unknown>
15:29:41  #15 0x55b2c70aa3d6 <unknown>
15:29:41  #16 0x55b2c70bb5e6 <unknown>
15:29:41  #17 0x7f2476ee6609 start_thread
15:29:41  
15:29:41    - Verify Login With Multiple Accounts From Excel: Variable '@{ROBOT_USERS_PANDAS}' not found.
15:29:41    - Verify successful create new program: SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:41  Stacktrace:
15:29:41  #0 0x55fb7a2dc2ca <unknown>
15:29:41  #1 0x55fb79d83550 <unknown>
15:29:41  #2 0x55fb79dbd9cb <unknown>
15:29:41  #3 0x55fb79db8d17 <unknown>
15:29:41  #4 0x55fb79e0919e <unknown>
15:29:41  #5 0x55fb79e08766 <unknown>
15:29:41  #6 0x55fb79dfa993 <unknown>
15:29:41  #7 0x55fb79dc6d6b <unknown>
15:29:41  #8 0x55fb79dc8141 <unknown>
15:29:41  #9 0x55fb7a2a12ab <unknown>
15:29:41  #10 0x55fb7a2a50b9 <unknown>
15:29:41  #11 0x55fb7a288139 <unknown>
15:29:41  #12 0x55fb7a2a5c68 <unknown>
15:29:41  #13 0x55fb7a26c60f <unknown>
15:29:41  #14 0x55fb7a2ca1f8 <unknown>
15:29:41  #15 0x55fb7a2ca3d6 <unknown>
15:29:41  #16 0x55fb7a2db5e6 <unknown>
15:29:41  #17 0x7ffafff6e609 start_thread
15:29:41  
15:29:41    - Verify create program fails when all fields are empty: SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:41  Stacktrace:
15:29:41  #0 0x556a6f28b2ca <unknown>
15:29:41  #1 0x556a6ed32550 <unknown>
15:29:41  #2 0x556a6ed6c9cb <unknown>
15:29:41  #3 0x556a6ed67d17 <unknown>
15:29:41  #4 0x556a6edb819e <unknown>
15:29:41  #5 0x556a6edb7766 <unknown>
15:29:41  #6 0x556a6eda9993 <unknown>
15:29:41  #7 0x556a6ed75d6b <unknown>
15:29:41  #8 0x556a6ed77141 <unknown>
15:29:41  #9 0x556a6f2502ab <unknown>
15:29:41  #10 0x556a6f2540b9 <unknown>
15:29:41  #11 0x556a6f237139 <unknown>
15:29:41  #12 0x556a6f254c68 <unknown>
15:29:41  #13 0x556a6f21b60f <unknown>
15:29:41  #14 0x556a6f2791f8 <unknown>
15:29:41  #15 0x556a6f2793d6 <unknown>
15:29:41  #16 0x556a6f28a5e6 <unknown>
15:29:41  #17 0x7fb73cc5e609 start_thread
15:29:41  
15:29:41    - Verify go to Program page: SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:41  Stacktrace:
15:29:41  #0 0x55de612392ca <unknown>
15:29:41  #1 0x55de60ce0550 <unknown>
15:29:41  #2 0x55de60d1a9cb <unknown>
15:29:41  #3 0x55de60d15d17 <unknown>
15:29:41  #4 0x55de60d6619e <unknown>
15:29:41  #5 0x55de60d65766 <unknown>
15:29:41  #6 0x55de60d57993 <unknown>
15:29:41  #7 0x55de60d23d6b <unknown>
15:29:41  #8 0x55de60d25141 <unknown>
15:29:41  #9 0x55de611fe2ab <unknown>
15:29:41  #10 0x55de612020b9 <unknown>
15:29:41  #11 0x55de611e5139 <unknown>
15:29:41  #12 0x55de61202c68 <unknown>
15:29:41  #13 0x55de611c960f <unknown>
15:29:41  #14 0x55de612271f8 <unknown>
15:29:41  #15 0x55de612273d6 <unknown>
15:29:41  #16 0x55de612385e6 <unknown>
15:29:41  #17 0x7f3777eef609 start_thread
15:29:41  
15:29:41    - Search for program by Name: SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:41  Stacktrace:
15:29:41  #0 0x557b061b02ca <unknown>
15:29:41  #1 0x557b05c57550 <unknown>
15:29:41  #2 0x557b05c919cb <unknown>
15:29:41  #3 0x557b05c8cd17 <unknown>
15:29:41  #4 0x557b05cdd19e <unknown>
15:29:41  #5 0x557b05cdc766 <unknown>
15:29:41  #6 0x557b05cce993 <unknown>
15:29:41  #7 0x557b05c9ad6b <unknown>
15:29:41  #8 0x557b05c9c141 <unknown>
15:29:41  #9 0x557b061752ab <unknown>
15:29:41  #10 0x557b061790b9 <unknown>
15:29:41  #11 0x557b0615c139 <unknown>
15:29:41  #12 0x557b06179c68 <unknown>
15:29:41  #13 0x557b0614060f <unknown>
15:29:41  #14 0x557b0619e1f8 <unknown>
15:29:41  #15 0x557b0619e3d6 <unknown>
15:29:41  #16 0x557b061af5e6 <unknown>
15:29:41  #17 0x7fb046b17609 start_thread
15:29:41  
15:29:41    - Search for program not found: SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:29:41  Stacktrace:
15:29:41  #0 0x56327549d2ca <unknown>
15:29:41  #1 0x563274f44550 <unknown>
15:29:41  #2 0x563274f7e9cb <unknown>
15:29:41  #3 0x563274f79d17 <unknown>
15:29:41  #4 0x563274fca19e <unknown>
15:29:41  #5 0x563274fc9766 <unknown>
15:29:41  #6 0x563274fbb993 <unknown>
15:29:41  #7 0x563274f87d6b <unknown>
15:29:41  #8 0x563274f89141 <unknown>
15:29:41  #9 0x5632754622ab <unknown>
15:29:41  #10 0x5632754660b9 <unknown>
15:29:41  #11 0x563275449139 <unknown>
15:29:41  #12 0x563275466c68 <unknown>
15:29:41  #13 0x56327542d60f <unknown>
15:29:41  #14 0x56327548b1f8 <unknown>
15:29:41  #15 0x56327548b3d6 <unknown>
15:29:41  #16 0x56327549c5e6 <unknown>
15:29:41  #17 0x7fc1035bd609 start_thread
15:29:41  
15:29:41  Sending test results email...
15:29:41  Email sent <NAME_EMAIL>
15:29:41  ============================================================
15:29:41  TEST EXECUTION CYCLE COMPLETED
15:29:41  Tests executed: With errors
15:29:41  Email sent: Yes
15:29:41  ============================================================
15:29:41  
15:29:41  ========================================
15:29:41  FINAL SUMMARY
15:29:41  ========================================
15:29:41  Tests Run: 34
15:29:41  Passed: 0
15:29:41  Failed: 34
15:29:41  Success Rate: 0.00%
15:29:41  Overall Status: FAIL
15:29:41  Email Sent: Yes
15:29:41  + echo Tests completed with some failures
15:29:41  Tests completed with some failures
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Publish Results)
[Pipeline] echo
15:29:42  Publishing test results...
[Pipeline] archiveArtifacts
15:29:42  Archiving artifacts
[Pipeline] script
[Pipeline] {
[Pipeline] fileExists
[Pipeline] }
[Pipeline] // script
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Declarative: Post Actions)
[Pipeline] echo
15:29:42  Pipeline completed
[Pipeline] echo
15:29:42  Pipeline failed!
[Pipeline] }
[Pipeline] // stage
[Pipeline] }
[Pipeline] // timestamps
[Pipeline] }
[Pipeline] // timeout
[Pipeline] }
[Pipeline] // withEnv
[Pipeline] }
[Pipeline] // withEnv
[Pipeline] }
[Pipeline] // node
[Pipeline] End of Pipeline
Also:   org.jenkinsci.plugins.workflow.actions.ErrorAction$ErrorId: de6982d1-971d-4aa0-8180-cfc27ca521fc
java.lang.NoSuchMethodError: No such DSL method 'publishHTML' found among steps [archive, bat, build, catchError, checkout, deleteDir, dir, echo, emailext, emailextrecipients, envVarsForTool, error, fileExists, findBuildScans, getContext, git, input, isUnix, junit, library, libraryResource, load, mail, milestone, node, parallel, powershell, properties, publishChecks, pwd, pwsh, readFile, readTrusted, resolveScm, retry, script, sh, sleep, stage, stash, step, timeout, timestamps, tm, tool, unarchive, unstable, unstash, validateDeclarativePipeline, waitForBuild, waitUntil, warnError, withChecks, withContext, withCredentials, withEnv, withGradle, wrap, writeFile, ws] or symbols [GitUsernamePassword, agent, all, allBranchesSame, allOf, always, ant, antFromApache, antOutcome, antTarget, any, anyOf, apiToken, apiTokenProperty, architecture, archiveArtifacts, artifactManager, assembla, authorInChangelog, authorizationMatrix, batchFile, bitbucket, bitbucketServer, booleanParam, branch, brokenBuildSuspects, brokenTestsSuspects, browser, buildButton, buildDiscarder, buildDiscarders, buildRetention, buildSingleRevisionOnly, buildUser, buildingTag, builtInNode, caseInsensitive, caseSensitive, certificate, cgit, changeRequest, changelog, changelogBase, changelogToBranch, changeset, checkoutOption, checkoutToSubdirectory, choice, choiceParam, cleanAfterCheckout, cleanBeforeCheckout, cleanWs, clock, cloneOption, command, computerRetentionCheckInterval, consoleUrlProvider, contributor, cps, credentials, cron, crumb, culprits, dark, darkSystem, default, defaultDisplayUrlProvider, defaultFolderConfiguration, defaultView, demand, developers, disableConcurrentBuilds, disableRestartFromStage, disableResume, discoverOtherRefs, discoverOtherRefsTrait, diskSpace, diskSpaceMonitor, downstream, dumb, durabilityHint, email-ext, envVars, envVarsFilter, environment, equals, experimentalFlags, expression, extendedEmailPublisher, file, fileParam, filePath, fingerprint, fingerprints, firstBuildChangelog, fisheye, frameOptions, freeStyle, freeStyleJob, fromScm, fromSource, git, gitBranchDiscovery, gitHooks, gitHubBranchDiscovery, gitHubBranchHeadAuthority, gitHubExcludeArchivedRepositories, gitHubExcludeForkedRepositories, gitHubExcludePrivateRepositories, gitHubExcludePublicRepositories, gitHubForkDiscovery, gitHubIgnoreDraftPullRequestFilter, gitHubPullRequestDiscovery, gitHubSshCheckout, gitHubTagDiscovery, gitHubTopicsFilter, gitHubTrustContributors, gitHubTrustEveryone, gitHubTrustNobody, gitHubTrustPermissions, gitLab, gitList, gitSCM, gitTagDiscovery, gitTool, gitUsernamePassword, gitWeb, gitblit, github, githubProjectProperty, githubPush, gitiles, gogs, gradle, group, headRegexFilter, headWildcardFilter, hyperlink, hyperlinkToModels, ignoreOnPush, inbound, inheriting, inheritingGlobal, installSource, isRestartedRun, jdk, jgit, jgitapache, jnlp, jobBuildDiscarder, jobName, junitTestResultStorage, kiln, label, lastDuration, lastFailure, lastGrantedAuthorities, lastStable, lastSuccess, legacy, legacySCM, lfs, list, local, localBranch, localBranchTrait, location, logRotator, loggedInUsersCanDoAnything, mailer, masterBuild, maven, maven3Mojos, mavenErrors, mavenGlobalConfig, mavenMojos, mavenWarnings, modernSCM, multiBranchProjectDisplayNaming, multibranch, myView, namedBranchesDifferent, node, nodeProperties, nonInheriting, none, nonresumable, not, organizationFolder, overrideIndexTriggers, paneStatus, parallelsAlwaysFailFast, parameters, password, pattern, perBuildTag, permanent, phabricator, pipeline, pipelineGraphView, pipelineTriggers, plainText, plugin, pollSCM, preserveStashes, previous, projectNamingStrategy, proxy, pruneStaleBranch, pruneStaleTag, pruneTags, queueItemAuthenticator, quietPeriod, rateLimit, rateLimitBuilds, recipients, redmine, refSpecs, remoteName, requestor, resourceRoot, responseTime, retainOnlyVariables, rhodeCode, run, runParam, sSHLauncher, schedule, scmGit, scmRetryCount, scriptApproval, scriptApprovalLink, search, security, shell, simpleBuildDiscarder, skipDefaultCheckout, skipStagesAfterUnstable, slave, sourceRegexFilter, sourceWildcardFilter, sparseCheckout, sparseCheckoutPaths, ssh, sshUserPrivateKey, standard, status, string, stringParam, submodule, submoduleOption, suppressAutomaticTriggering, suppressFolderAutomaticTriggering, swapSpace, tag, teamFoundation, teamSlugFilter, text, textParam, themeManager, timestamper, timestamperConfig, timezone, tmpSpace, toolLocation, triggeredBy, unsecured, untrusted, upstream, upstreamDevelopers, user, userIdentity, userOrGroup, userSeed, usernameColonPassword, usernamePassword, viewgit, viewsTabBar, weather, withAnt, zip] or globals [currentBuild, env, params, pipeline, scm]
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.DSL.invokeMethod(DSL.java:219)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsScript.invokeMethod(CpsScript.java:124)
	at jdk.internal.reflect.GeneratedMethodAccessor4214.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:98)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1225)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1034)
	at org.codehaus.groovy.runtime.callsite.PogoMetaClassSite.call(PogoMetaClassSite.java:41)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:116)
	at PluginClassLoader for script-security//org.kohsuke.groovy.sandbox.impl.Checker$1.call(Checker.java:180)
	at PluginClassLoader for script-security//org.kohsuke.groovy.sandbox.GroovyInterceptor.onMethodCall(GroovyInterceptor.java:23)
	at PluginClassLoader for script-security//org.jenkinsci.plugins.scriptsecurity.sandbox.groovy.SandboxInterceptor.onMethodCall(SandboxInterceptor.java:163)
	at PluginClassLoader for script-security//org.kohsuke.groovy.sandbox.impl.Checker$1.call(Checker.java:178)
	at PluginClassLoader for script-security//org.kohsuke.groovy.sandbox.impl.Checker.checkedCall(Checker.java:182)
	at PluginClassLoader for script-security//org.kohsuke.groovy.sandbox.impl.Checker.checkedCall(Checker.java:152)
	at PluginClassLoader for script-security//org.kohsuke.groovy.sandbox.impl.Checker.checkedCall(Checker.java:152)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.sandbox.SandboxInvoker.methodCall(SandboxInvoker.java:17)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.LoggingInvoker.methodCall(LoggingInvoker.java:117)
	at WorkflowScript.run(WorkflowScript:51)
	at ___cps.transform___(Native Method)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.impl.ContinuationGroup.methodCall(ContinuationGroup.java:90)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.impl.FunctionCallBlock$ContinuationImpl.dispatchOrArg(FunctionCallBlock.java:116)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.impl.FunctionCallBlock$ContinuationImpl.fixArg(FunctionCallBlock.java:85)
	at jdk.internal.reflect.GeneratedMethodAccessor3120.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.impl.ContinuationPtr$ContinuationImpl.receive(ContinuationPtr.java:72)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.impl.CollectionLiteralBlock$ContinuationImpl.dispatch(CollectionLiteralBlock.java:55)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.impl.CollectionLiteralBlock$ContinuationImpl.item(CollectionLiteralBlock.java:45)
	at jdk.internal.reflect.GeneratedMethodAccessor3221.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.impl.ContinuationPtr$ContinuationImpl.receive(ContinuationPtr.java:72)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.impl.ConstantBlock.eval(ConstantBlock.java:21)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.Next.step(Next.java:83)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.Continuable.run0(Continuable.java:147)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.SandboxContinuable.access$001(SandboxContinuable.java:17)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.SandboxContinuable.run0(SandboxContinuable.java:49)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsThread.runNextChunk(CpsThread.java:180)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsThreadGroup.run(CpsThreadGroup.java:422)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsThreadGroup$2.call(CpsThreadGroup.java:330)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsThreadGroup$2.call(CpsThreadGroup.java:294)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsVmExecutorService.lambda$wrap$4(CpsVmExecutorService.java:140)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at hudson.remoting.SingleLaneExecutorService$1.run(SingleLaneExecutorService.java:139)
	at jenkins.util.ContextResettingExecutorService$1.run(ContextResettingExecutorService.java:28)
	at jenkins.security.ImpersonatingExecutorService$1.run(ImpersonatingExecutorService.java:68)
	at jenkins.util.ErrorLoggingExecutorService.lambda$wrap$0(ErrorLoggingExecutorService.java:51)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsVmExecutorService$1.call(CpsVmExecutorService.java:53)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsVmExecutorService$1.call(CpsVmExecutorService.java:50)
	at org.codehaus.groovy.runtime.GroovyCategorySupport$ThreadCategoryInfo.use(GroovyCategorySupport.java:136)
	at org.codehaus.groovy.runtime.GroovyCategorySupport.use(GroovyCategorySupport.java:275)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsVmExecutorService.lambda$categoryThreadFactory$0(CpsVmExecutorService.java:50)
	at java.base/java.lang.Thread.run(Thread.java:840)
Finished: FAILURE
