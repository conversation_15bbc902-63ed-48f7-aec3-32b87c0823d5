#!/usr/bin/env python3
"""
Script to cleanup Chrome processes and temporary directories before running tests
"""
import os
import subprocess
import tempfile
import shutil
import glob
import time

def kill_chrome_processes():
    """Kill all Chrome and ChromeDriver processes"""
    print("Killing Chrome and ChromeDriver processes...")
    
    processes_to_kill = [
        'chrome',
        'chromium',
        'chromium-browser',
        'google-chrome',
        'google-chrome-stable',
        'chromedriver'
    ]
    
    for process in processes_to_kill:
        try:
            # Kill process by name
            subprocess.run(['pkill', '-f', process], 
                         capture_output=True, check=False)
            print(f"  - Killed {process} processes")
        except Exception as e:
            print(f"  - Could not kill {process}: {e}")
    
    # Wait a moment for processes to terminate
    time.sleep(2)

def cleanup_chrome_temp_dirs():
    """Clean up Chrome temporary directories"""
    print("Cleaning up Chrome temporary directories...")
    
    temp_dir = tempfile.gettempdir()
    
    # Patterns to match Chrome temp directories
    patterns = [
        os.path.join(temp_dir, 'chrome_user_data_*'),
        os.path.join(temp_dir, '.com.google.Chrome.*'),
        os.path.join(temp_dir, 'scoped_dir*'),
        '/tmp/chrome_user_data_*',
        '/tmp/.com.google.Chrome.*'
    ]
    
    for pattern in patterns:
        try:
            for path in glob.glob(pattern):
                if os.path.isdir(path):
                    shutil.rmtree(path, ignore_errors=True)
                    print(f"  - Removed directory: {path}")
                elif os.path.isfile(path):
                    os.remove(path)
                    print(f"  - Removed file: {path}")
        except Exception as e:
            print(f"  - Could not clean pattern {pattern}: {e}")

def cleanup_user_data_dirs():
    """Clean up user-specific Chrome data directories"""
    print("Cleaning up user Chrome data directories...")
    
    home_dir = os.path.expanduser('~')
    
    # Common Chrome user data locations
    chrome_dirs = [
        os.path.join(home_dir, '.config', 'google-chrome'),
        os.path.join(home_dir, '.config', 'chromium'),
        os.path.join(home_dir, '.cache', 'google-chrome'),
        os.path.join(home_dir, '.cache', 'chromium')
    ]
    
    for chrome_dir in chrome_dirs:
        if os.path.exists(chrome_dir):
            try:
                # Only remove specific subdirectories that might cause conflicts
                conflict_dirs = [
                    os.path.join(chrome_dir, 'SingletonLock'),
                    os.path.join(chrome_dir, 'SingletonSocket'),
                    os.path.join(chrome_dir, 'Default', 'SingletonLock'),
                    os.path.join(chrome_dir, 'Default', 'SingletonSocket')
                ]
                
                for conflict_dir in conflict_dirs:
                    if os.path.exists(conflict_dir):
                        if os.path.isfile(conflict_dir):
                            os.remove(conflict_dir)
                            print(f"  - Removed lock file: {conflict_dir}")
                        elif os.path.isdir(conflict_dir):
                            shutil.rmtree(conflict_dir, ignore_errors=True)
                            print(f"  - Removed lock directory: {conflict_dir}")
                            
            except Exception as e:
                print(f"  - Could not clean {chrome_dir}: {e}")

def main():
    """Main cleanup function"""
    print("Chrome Cleanup Script")
    print("=" * 30)
    
    try:
        kill_chrome_processes()
        cleanup_chrome_temp_dirs()
        cleanup_user_data_dirs()
        
        print("\n✓ Chrome cleanup completed successfully")
        return True
        
    except Exception as e:
        print(f"\n✗ Chrome cleanup failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
