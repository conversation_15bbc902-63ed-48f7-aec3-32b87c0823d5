# variables/browser_options.py
from selenium.webdriver.chrome.options import Options

def get_chrome_options():
    import uuid
    import tempfile
    import os

    options = Options()

    # Fix for "user data directory is already in use" error
    # Create unique user data directory for each test session
    temp_dir = tempfile.gettempdir()
    unique_id = str(uuid.uuid4())[:8]
    user_data_dir = os.path.join(temp_dir, f"chrome_user_data_{unique_id}")
    options.add_argument(f"--user-data-dir={user_data_dir}")

    # Essential headless options for CI/CD
    options.add_argument("--headless")  # Run in headless mode
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-gpu")  # Disable GPU for headless
    options.add_argument("--window-size=1920,1080")  # Set window size

    # Security and stability options
    options.add_argument("--disable-web-security")
    options.add_argument("--disable-features=VizDisplayCompositor")
    options.add_argument("--disable-notifications")
    options.add_argument("--disable-extensions")
    options.add_argument("--disable-plugins")
    options.add_argument("--disable-images")  # Speed up by not loading images

    # Additional stability options
    options.add_argument("--ignore-ssl-errors=yes")
    options.add_argument("--allow-insecure-localhost")
    options.add_argument("--ignore-certificate-errors")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_argument("--remote-debugging-port=0")  # Use random port

    # User agent
    options.add_argument('--user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
    # Chrome preferences
    prefs = {
        "credentials_enable_service": False,
        "profile.password_manager_enabled": False,
        "profile.default_content_settings.popups": 0,
        "profile.default_content_setting_values.notifications": 2,  # Block notifications
        "profile.managed_default_content_settings.images": 2,  # Block images for speed
    }

    # Experimental options
    options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
    options.add_experimental_option("useAutomationExtension", False)
    options.add_experimental_option("prefs", prefs)

    # Disable logging
    options.add_argument("--log-level=3")  # Suppress INFO, WARNING, ERROR
    options.add_argument("--silent")

    return options


# # # Hàm get_variables() mà Robot Framework sẽ gọi để lấy các biến động
# def get_variables():
#     return {
#         "OPTIONS": get_chrome_options()
#     }

# import keyword robot
# from robot.api.deco import keyword

# # create class options
# class browser_options:
#     def __init__(self):
#         self.options = Options()
        
#     @keyword('Get Options')
#     def get_options(self):
#         return self.options.add_argument("--incognito")
    