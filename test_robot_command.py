#!/usr/bin/env python3
"""
Simple script to test if robot command is available
"""
import os
import sys
import shutil
import subprocess

def test_robot_command():
    """Test different ways to run robot command"""
    print("Testing Robot Framework command availability...")
    print("=" * 50)
    
    # Method 1: Check if robot is in PATH
    robot_path = shutil.which('robot')
    if robot_path:
        print(f"✓ Found robot command at: {robot_path}")
        try:
            result = subprocess.run([robot_path, '--version'], capture_output=True, text=True)
            print(f"✓ Robot version: {result.stdout.strip()}")
            return robot_path, []
        except Exception as e:
            print(f"✗ Error running robot command: {e}")
    else:
        print("✗ robot command not found in PATH")
    
    # Method 2: Check common locations
    print("\nChecking common installation locations...")
    possible_paths = [
        os.path.expanduser('~/.local/bin/robot'),
        '/usr/local/bin/robot',
        '/usr/bin/robot'
    ]
    
    for path in possible_paths:
        if os.path.isfile(path) and os.access(path, os.X_OK):
            print(f"✓ Found robot at: {path}")
            try:
                result = subprocess.run([path, '--version'], capture_output=True, text=True)
                print(f"✓ Robot version: {result.stdout.strip()}")
                return path, []
            except Exception as e:
                print(f"✗ Error running robot at {path}: {e}")
        else:
            print(f"✗ No robot found at: {path}")
    
    # Method 3: Try python -m robot
    print("\nTrying python -m robot...")
    try:
        result = subprocess.run([sys.executable, '-m', 'robot', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ python -m robot works!")
            print(f"✓ Robot version: {result.stdout.strip()}")
            return sys.executable, ['-m', 'robot']
        else:
            print(f"✗ python -m robot failed: {result.stderr}")
    except Exception as e:
        print(f"✗ Error running python -m robot: {e}")
    
    print("\n✗ Robot Framework not found or not working!")
    return None, []

def test_requirements():
    """Test if requirements are installed"""
    print("\n" + "=" * 50)
    print("Testing installed packages...")
    
    try:
        import robot
        print(f"✓ robotframework imported successfully")
        print(f"✓ Robot Framework version: {robot.__version__}")
    except ImportError:
        print("✗ robotframework not installed")
    
    try:
        import SeleniumLibrary
        print(f"✓ SeleniumLibrary imported successfully")
    except ImportError:
        print("✗ robotframework-seleniumlibrary not installed")
    
    try:
        import gspread
        print(f"✓ gspread imported successfully")
    except ImportError:
        print("✗ gspread not installed")

if __name__ == "__main__":
    robot_cmd, robot_args = test_robot_command()
    test_requirements()
    
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    
    if robot_cmd:
        print(f"✓ Robot command: {robot_cmd} {' '.join(robot_args)}")
        print("✓ Robot Framework is ready to use!")
    else:
        print("✗ Robot Framework is not available")
        print("Please install robotframework: pip3 install --user robotframework")
