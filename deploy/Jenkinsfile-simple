pipeline {
    agent any
    
    environment {
        RESULTS_DIR = "results"
    }
    
    options {
        buildDiscarder(logRotator(daysToKeepStr: '30', numToKeepStr: '50'))
        timeout(time: 30, unit: 'MINUTES')
        timestamps()
    }
    
    stages {
        stage('Setup & Run Tests') {
            steps {
                echo 'Setting up environment and running tests...'
                sh '''
                    # Check Python
                    python3 --version

                    # Install dependencies directly (no virtual environment)
                    pip3 install --user --upgrade pip
                    pip3 install --user -r requirements.txt

                    # Add user bin to PATH for robot command
                    export PATH=$HOME/.local/bin:$PATH

                    # Test robot command availability
                    python3 test_robot_command.py

                    # Cleanup Chrome processes and temp directories
                    python3 cleanup_chrome.py

                    # Install Xvfb for headless browser testing
                    sudo apt-get update || echo "apt-get update failed"
                    sudo apt-get install -y xvfb || echo "xvfb installation failed"

                    # Create results directory
                    mkdir -p ${RESULTS_DIR}

                    # Setup virtual display for headless browser testing
                    export DISPLAY=:99
                    Xvfb :99 -screen 0 1920x1080x24 &
                    XVFB_PID=$!
                    echo "Started Xvfb with PID: $XVFB_PID"

                    # Wait for Xvfb to start
                    sleep 3

                    # Run tests (handle failures gracefully)
                    python3 run_tests.py || echo "Tests completed with some failures"

                    # Kill Xvfb
                    kill $XVFB_PID || echo "Failed to kill Xvfb"
                '''
            }
        }
        
        stage('Publish Results') {
            steps {
                echo 'Publishing test results...'
                
                // Archive test results
                archiveArtifacts artifacts: 'results/**/*', allowEmptyArchive: true
                
                // Publish HTML report if available
                script {
                    if (fileExists('results/report.html')) {
                        publishHTML([
                            allowMissing: false,
                            alwaysLinkToLastBuild: true,
                            keepAll: true,
                            reportDir: 'results',
                            reportFiles: 'report.html',
                            reportName: 'Test Report'
                        ])
                        echo 'Test report published'
                    } else {
                        echo 'No test report found'
                    }
                }
            }
        }
    }
    
    post {
        always {
            echo 'Pipeline completed'
        }
        
        success {
            echo 'Pipeline succeeded!'
        }
        
        failure {
            echo 'Pipeline failed!'
        }
    }
}
