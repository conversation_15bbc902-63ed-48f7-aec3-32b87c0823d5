pipeline {
    agent any
    
    environment {
        RESULTS_DIR = "results"
    }
    
    options {
        buildDiscarder(logRotator(daysToKeepStr: '30', numToKeepStr: '50'))
        timeout(time: 30, unit: 'MINUTES')
        timestamps()
    }
    
    stages {
        stage('Setup & Run Tests') {
            steps {
                echo 'Setting up environment and running tests...'
                sh '''
                    # Check Python
                    python3 --version
                    
                    # Install dependencies directly (no virtual environment)
                    pip3 install --user --upgrade pip
                    pip3 install --user -r requirements.txt
                    
                    # Add user bin to PATH for robot command
                    export PATH=$HOME/.local/bin:$PATH
                    
                    # Test robot command availability
                    python3 test_robot_command.py

                    # Cleanup Chrome processes and temp directories
                    python3 cleanup_chrome.py

                    # Create results directory
                    mkdir -p ${RESULTS_DIR}
                    
                    # Setup virtual display (if Xvfb is available)
                    if command -v xvfb-run >/dev/null 2>&1; then
                        echo "Using xvfb-run for headless testing"
                        xvfb-run -a -s "-screen 0 1920x1080x24" python3 run_tests.py || echo "Tests completed with some failures"
                    else
                        echo "No Xvfb available, running tests directly (may fail if GUI is required)"
                        export DISPLAY=:0
                        python3 run_tests.py || echo "Tests completed with some failures"
                    fi
                '''
            }
        }
        
        stage('Publish Results') {
            steps {
                echo 'Publishing test results...'
                
                // Archive test results
                archiveArtifacts artifacts: 'results/**/*', allowEmptyArchive: true
                
                // Publish HTML report if available
                script {
                    if (fileExists('results/report.html')) {
                        publishHTML([
                            allowMissing: false,
                            alwaysLinkToLastBuild: true,
                            keepAll: true,
                            reportDir: 'results',
                            reportFiles: 'report.html',
                            reportName: 'Test Report'
                        ])
                        echo 'Test report published'
                    } else {
                        echo 'No test report found'
                    }
                }
            }
        }
    }
    
    post {
        always {
            echo 'Pipeline completed'
        }
        
        success {
            echo 'Pipeline succeeded!'
        }
        
        failure {
            echo 'Pipeline failed!'
        }
    }
}
