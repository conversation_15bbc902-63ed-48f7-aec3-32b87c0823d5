#!/bin/bash

# Script to switch between different Jenkinsfile versions
# Usage: ./switch-pipeline.sh [venv|simple]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}Jenkins Pipeline Switcher${NC}"
echo "========================="

# Check argument
if [ $# -eq 0 ]; then
    echo -e "${YELLOW}Usage: $0 [venv|simple|no-sudo]${NC}"
    echo ""
    echo "Available options:"
    echo "  venv     - Use virtual environment (requires python3-venv or sudo access)"
    echo "  simple   - Use direct pip install + Xvfb (requires sudo for Xvfb)"
    echo "  no-sudo  - Use direct pip install without sudo (uses xvfb-run if available)"
    echo ""
    exit 1
fi

case "$1" in
    "venv")
        echo -e "${YELLOW}Switching to virtual environment Jenkinsfile...${NC}"
        cp Jenkinsfile ../Jenkinsfile
        echo -e "${GREEN}✓ Virtual environment Jenkinsfile activated${NC}"
        echo ""
        echo "Requirements:"
        echo "- python3-venv package installed OR sudo access to install it"
        echo "- Jenkins must have Python 3.x installed"
        ;;
    "simple")
        echo -e "${YELLOW}Switching to simple Jenkinsfile (with sudo for Xvfb)...${NC}"
        cp Jenkinsfile-simple ../Jenkinsfile
        echo -e "${GREEN}✓ Simple Jenkinsfile activated${NC}"
        echo ""
        echo "Requirements:"
        echo "- Jenkins must have Python 3.x and pip3 installed"
        echo "- Sudo access required for Xvfb installation"
        echo "- Uses --user flag for pip install"
        ;;
    "no-sudo")
        echo -e "${YELLOW}Switching to no-sudo Jenkinsfile...${NC}"
        cp Jenkinsfile-no-sudo ../Jenkinsfile
        echo -e "${GREEN}✓ No-sudo Jenkinsfile activated${NC}"
        echo ""
        echo "Requirements:"
        echo "- Jenkins must have Python 3.x and pip3 installed"
        echo "- No sudo access required"
        echo "- Uses xvfb-run if available, otherwise runs without display"
        ;;
    *)
        echo -e "${RED}Error: Invalid option '$1'${NC}"
        echo "Use 'venv', 'simple', or 'no-sudo'"
        exit 1
        ;;
esac

echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "1. Commit and push the updated Jenkinsfile"
echo "2. Run your Jenkins pipeline"
echo ""
echo -e "${YELLOW}Note: The Jenkinsfile has been copied to the project root${NC}"
