pipeline {
    agent any

    environment {
        RESULTS_DIR = "results"
        VENV_PATH = "venv"
    }

    options {
        buildDiscarder(logRotator(daysToKeepStr: '30', numToKeepStr: '50'))
        timeout(time: 30, unit: 'MINUTES')
        timestamps()
    }

    stages {
        stage('Setup & Run Tests') {
            steps {
                echo 'Setting up environment and running tests...'
                sh '''
                    # Check Python
                    python3 --version

                    # Install python3-venv if not available
                    if ! python3 -m venv --help >/dev/null 2>&1; then
                        echo "Installing python3-venv..."
                        sudo apt-get update
                        sudo apt-get install -y python3-venv python3-pip
                    fi

                    # Create virtual environment
                    python3 -m venv ${VENV_PATH}
                    . ${VENV_PATH}/bin/activate

                    # Install dependencies
                    pip install --upgrade pip
                    pip install -r requirements.txt

                    # Create results directory
                    mkdir -p ${RESULTS_DIR}

                    # Run tests (handle failures gracefully)
                    python run_tests.py || echo "Tests completed with some failures"
                '''
            }
        }

        stage('Publish Results') {
            steps {
                echo 'Publishing test results...'

                // Archive test results
                archiveArtifacts artifacts: 'results/**/*', allowEmptyArchive: true

                // Publish HTML report if available
                script {
                    if (fileExists('results/report.html')) {
                        publishHTML([
                            allowMissing: false,
                            alwaysLinkToLastBuild: true,
                            keepAll: true,
                            reportDir: 'results',
                            reportFiles: 'report.html',
                            reportName: 'Test Report'
                        ])
                        echo 'Test report published'
                    } else {
                        echo 'No test report found'
                    }
                }
            }
        }
    }

    post {
        always {
            echo 'Cleaning up...'
            sh 'rm -rf ${VENV_PATH} || true'
        }

        success {
            echo 'Pipeline succeeded!'
        }

        failure {
            echo 'Pipeline failed!'
        }
    }
}
