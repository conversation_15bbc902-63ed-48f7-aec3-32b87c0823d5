pipeline {
    agent any
    
    environment {
        RESULTS_DIR = "results"
    }
    
    options {
        buildDiscarder(logRotator(daysToKeepStr: '30', numToKeepStr: '50'))
        timeout(time: 30, unit: 'MINUTES')
        timestamps()
    }
    
    stages {
        stage('Setup & Run Tests') {
            steps {
                echo 'Setting up environment and running tests...'
                sh '''
                    # Check Python
                    python3 --version

                    # Install dependencies directly (no virtual environment)
                    pip3 install --user --upgrade pip
                    pip3 install --user -r requirements.txt

                    # Add user bin to PATH for robot command
                    export PATH=$HOME/.local/bin:$PATH

                    # Test robot command availability
                    python3 test_robot_command.py

                    # Create results directory
                    mkdir -p ${RESULTS_DIR}

                    # Run tests (handle failures gracefully)
                    python3 run_tests.py || echo "Tests completed with some failures"
                '''
            }
        }
        
        stage('Publish Results') {
            steps {
                echo 'Publishing test results...'
                
                // Archive test results
                archiveArtifacts artifacts: 'results/**/*', allowEmptyArchive: true
                
                // Publish HTML report if available
                script {
                    if (fileExists('results/report.html')) {
                        publishHTML([
                            allowMissing: false,
                            alwaysLinkToLastBuild: true,
                            keepAll: true,
                            reportDir: 'results',
                            reportFiles: 'report.html',
                            reportName: 'Test Report'
                        ])
                        echo 'Test report published'
                    } else {
                        echo 'No test report found'
                    }
                }
            }
        }
    }
    
    post {
        always {
            echo 'Pipeline completed'
        }
        
        success {
            echo 'Pipeline succeeded!'
        }
        
        failure {
            echo 'Pipeline failed!'
        }
    }
}
